"""
降雨空间插值系统 - OI插值算法

最优插值法（Optimal Interpolation）的实现。

作者: Augment Agent
日期: 2025-06-26
"""

import numpy as np
from scipy.spatial.distance import cdist
from scipy.linalg import solve, LinAlgError
from typing import Tuple, Optional, List
import warnings

from ..utils.error_handler import InterpolationError, ErrorHandler
from .idw import IDWInterpolator


class OIInterpolator:
    """最优插值器"""
    
    def __init__(self, config: dict, error_handler: Optional[ErrorHandler] = None):
        self.config = config
        self.error_handler = error_handler or ErrorHandler()
        
        # 获取配置参数
        oi_config = config['method_params']['OI']
        self.observation_error_variance = oi_config['observation_error_variance']
        self.background_error_corr_length = oi_config['background_error_corr_length']
        
        # 创建IDW插值器用于生成背景场
        self.idw_interpolator = IDWInterpolator(config, error_handler)
    
    def interpolate(self, station_coords: np.ndarray, station_values: np.ndarray,
                   target_coords: np.ndarray, mask: Optional[np.ndarray] = None) -> np.ndarray:
        """
        执行OI插值
        
        参数:
            station_coords: 站点坐标 (N, 2)
            station_values: 站点值 (N,)
            target_coords: 目标点坐标 (M, 2)
            mask: 掩膜数组
            
        返回:
            插值结果数组 (M,)
        """
        try:
            # 检查输入数据
            self._validate_inputs(station_coords, station_values, target_coords)
            
            # 检查是否所有站点值都为零
            if np.all(station_values == 0):
                self.error_handler.log_info("所有站点值为零，直接返回零值栅格")
                return np.zeros(len(target_coords))
            
            # 过滤异常值
            valid_mask = np.isfinite(station_values) & (station_values >= 0)
            if not valid_mask.all():
                station_coords = station_coords[valid_mask]
                station_values = station_values[valid_mask]
                self.error_handler.log_info(f"过滤异常值，剩余 {len(station_values)} 个有效站点")
            
            if len(station_values) < 2:
                raise InterpolationError("有效站点数量不足，无法进行OI插值")
            
            # 步骤1：生成背景场
            background_field = self._generate_background_field(
                station_coords, station_values, target_coords
            )
            
            # 步骤2：计算观测误差协方差矩阵R
            R = self._compute_observation_error_covariance(station_coords)
            
            # 步骤3：计算背景场误差协方差矩阵B
            B = self._compute_background_error_covariance(
                station_coords, station_values, background_field, target_coords
            )
            
            # 步骤4：执行最优插值
            analysis_field = self._perform_optimal_interpolation(
                station_coords, station_values, target_coords, 
                background_field, B, R
            )
            
            # 应用掩膜
            if mask is not None:
                analysis_field = self._apply_mask(analysis_field, mask)
            
            return analysis_field
            
        except Exception as e:
            raise InterpolationError(f"OI插值失败: {str(e)}")
    
    def _validate_inputs(self, station_coords: np.ndarray, station_values: np.ndarray,
                        target_coords: np.ndarray) -> None:
        """验证输入数据"""
        if len(station_coords) == 0:
            raise InterpolationError("站点坐标为空")
        
        if len(station_values) == 0:
            raise InterpolationError("站点值为空")
        
        if len(station_coords) != len(station_values):
            raise InterpolationError("站点坐标和值的数量不匹配")
        
        if len(target_coords) == 0:
            raise InterpolationError("目标坐标为空")
        
        if station_coords.shape[1] != 2 or target_coords.shape[1] != 2:
            raise InterpolationError("坐标数据必须是2维的")
    
    def _generate_background_field(self, station_coords: np.ndarray, station_values: np.ndarray,
                                  target_coords: np.ndarray) -> np.ndarray:
        """使用IDW生成背景场"""
        try:
            self.error_handler.log_info("使用IDW生成背景场")
            background_field = self.idw_interpolator.interpolate(
                station_coords, station_values, target_coords
            )
            return background_field
            
        except Exception as e:
            raise InterpolationError(f"生成背景场失败: {str(e)}")
    
    def _compute_observation_error_covariance(self, station_coords: np.ndarray) -> np.ndarray:
        """计算观测误差协方差矩阵R"""
        try:
            n_stations = len(station_coords)
            
            # 假设观测误差相互独立，R为对角矩阵
            R = np.eye(n_stations) * self.observation_error_variance
            
            return R
            
        except Exception as e:
            raise InterpolationError(f"计算观测误差协方差矩阵失败: {str(e)}")
    
    def _compute_background_error_covariance(self, station_coords: np.ndarray, 
                                           station_values: np.ndarray,
                                           background_field: np.ndarray,
                                           target_coords: np.ndarray) -> np.ndarray:
        """计算背景场误差协方差矩阵B"""
        try:
            # 计算背景场在站点位置的值
            background_at_stations = self._interpolate_background_to_stations(
                background_field, target_coords, station_coords
            )
            
            # 计算背景场误差
            background_errors = station_values - background_at_stations
            
            # 估计背景场误差方差
            background_error_variance = np.var(background_errors)
            
            if background_error_variance <= 0:
                background_error_variance = 1.0  # 设置默认值
                self.error_handler.log_warning("背景场误差方差为零或负值，使用默认值")
            
            # 计算站点间距离
            distances = cdist(station_coords, station_coords)
            
            # 使用指数衰减模型计算协方差
            B = background_error_variance * np.exp(-distances / self.background_error_corr_length)
            
            return B
            
        except Exception as e:
            raise InterpolationError(f"计算背景场误差协方差矩阵失败: {str(e)}")
    
    def _interpolate_background_to_stations(self, background_field: np.ndarray,
                                          target_coords: np.ndarray,
                                          station_coords: np.ndarray) -> np.ndarray:
        """将背景场插值到站点位置"""
        try:
            # 使用最近邻插值将背景场值插值到站点位置
            distances = cdist(station_coords, target_coords)
            nearest_indices = np.argmin(distances, axis=1)
            
            background_at_stations = background_field[nearest_indices]
            
            return background_at_stations
            
        except Exception as e:
            raise InterpolationError(f"背景场插值到站点失败: {str(e)}")
    
    def _perform_optimal_interpolation(self, station_coords: np.ndarray,
                                     station_values: np.ndarray,
                                     target_coords: np.ndarray,
                                     background_field: np.ndarray,
                                     B: np.ndarray, R: np.ndarray) -> np.ndarray:
        """执行最优插值计算"""
        try:
            # 计算背景场在站点位置的值
            background_at_stations = self._interpolate_background_to_stations(
                background_field, target_coords, station_coords
            )
            
            # 计算新息（观测值与背景场的差异）
            innovation = station_values - background_at_stations
            
            # 计算增益矩阵的分母 (H*B*H^T + R)
            # 这里H是单位矩阵，因为我们直接在站点位置进行比较
            denominator = B + R
            
            # 处理奇异矩阵问题
            try:
                # 尝试直接求解
                weights = solve(denominator, innovation)
            except LinAlgError:
                # 如果矩阵奇异，使用伪逆
                self.error_handler.log_warning("检测到奇异矩阵，使用伪逆求解")
                weights = np.linalg.pinv(denominator) @ innovation
            
            # 计算每个目标点的分析值
            analysis_field = np.zeros(len(target_coords))
            
            for i, target_coord in enumerate(target_coords):
                # 计算目标点与各站点的背景场误差协方差
                target_distances = cdist([target_coord], station_coords)[0]
                background_error_variance = np.var(station_values - background_at_stations)
                
                if background_error_variance <= 0:
                    background_error_variance = 1.0
                
                target_covariances = background_error_variance * np.exp(
                    -target_distances / self.background_error_corr_length
                )
                
                # 计算分析值
                analysis_increment = np.dot(target_covariances, weights)
                analysis_field[i] = background_field[i] + analysis_increment
            
            # 确保结果非负
            analysis_field = np.maximum(analysis_field, 0)
            
            return analysis_field
            
        except Exception as e:
            raise InterpolationError(f"最优插值计算失败: {str(e)}")
    
    def _apply_mask(self, result: np.ndarray, mask: np.ndarray) -> np.ndarray:
        """应用掩膜到插值结果"""
        try:
            if len(result) == mask.size:
                mask_flat = mask.flatten()
                result[mask_flat == 0] = self.config['grid_settings']['nodata_value']
            
            return result
            
        except Exception as e:
            raise InterpolationError(f"应用掩膜失败: {str(e)}")
    
    def get_method_name(self) -> str:
        """获取方法名称"""
        return "OI"
    
    def get_parameters(self) -> dict:
        """获取当前参数"""
        return {
            'observation_error_variance': self.observation_error_variance,
            'background_error_corr_length': self.background_error_corr_length
        }
    
    def set_parameters(self, **params) -> None:
        """设置参数"""
        if 'observation_error_variance' in params:
            self.observation_error_variance = params['observation_error_variance']
        if 'background_error_corr_length' in params:
            self.background_error_corr_length = params['background_error_corr_length']
