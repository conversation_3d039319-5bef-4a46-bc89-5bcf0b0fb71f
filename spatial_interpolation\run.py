"""
降雨空间插值系统 - 主运行脚本

提供命令行接口，支持多种运行模式。

作者: Augment Agent
日期: 2025-06-26
"""

import argparse
import sys
import os
import yaml
import time
import signal
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional, Any

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from utils.error_handler import <PERSON><PERSON>r<PERSON><PERSON><PERSON>, InterpolationError, ConfigError
from utils.data_loader import DataLoader
from utils.raster_io import RasterIO
from core.idw import IDWInterpolator
from core.kriging import KrigingInterpolator
from core.oi import OIInterpolator
from core.prism import PRISMInterpolator
from optimization.sceua_optimizer import SCEUAOptimizer
from evaluation.validator import CrossValidator


class InterpolationSystem:
    """降雨空间插值系统主类"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.config = self._load_config()
        self.error_handler = ErrorHandler(self.config['error_handling']['verbose_errors'])
        
        # 初始化组件
        self.data_loader = DataLoader(self.config, self.error_handler)
        self.raster_io = RasterIO(self.config, self.error_handler)
        self.validator = CrossValidator(self.config, self.error_handler)
        
        # 插值器字典
        self.interpolators = {}
        
        # 时间限制
        self.max_raster_time = self.config['run_settings'].get('max_raster_time', 300)
        self.max_optimization_time = self.config['run_settings'].get('max_optimization_time', 1800)
        
        # 信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _load_config(self) -> dict:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            raise ConfigError(f"加载配置文件失败: {str(e)}")
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n接收到信号 {signum}，正在安全退出...")
        sys.exit(0)
    
    def _initialize_interpolators(self) -> None:
        """初始化插值器"""
        try:
            self.interpolators = {
                'IDW': IDWInterpolator(self.config, self.error_handler),
                'Kriging': KrigingInterpolator(self.config, self.error_handler),
                'OI': OIInterpolator(self.config, self.error_handler),
                'PRISM': PRISMInterpolator(self.config, self.error_handler)
            }
            
            # 为PRISM设置地形数据
            terrain_data = self.data_loader.load_terrain_data()
            dem_data, dem_profile = terrain_data['dem']
            slope_data, _ = terrain_data['slope']
            aspect_data, _ = terrain_data['aspect']
            
            self.interpolators['PRISM'].set_terrain_data(
                dem_data, slope_data, aspect_data, dem_profile
            )
            
            self.error_handler.log_info("插值器初始化完成")
            
        except Exception as e:
            raise InterpolationError(f"初始化插值器失败: {str(e)}")
    
    def run_single_method_single_flood(self, method: str, flood_event: str) -> None:
        """运行单一方法于单一洪水场次"""
        try:
            self.error_handler.log_info(f"开始处理: 方法={method}, 洪水场次={flood_event}")
            
            # 初始化插值器
            self._initialize_interpolators()
            
            if method not in self.interpolators:
                raise InterpolationError(f"未知的插值方法: {method}")
            
            # 加载数据
            rainfall_data = self.data_loader.load_rainfall_data(flood_event)
            station_data = self.data_loader.load_station_data()
            delaunay_data = self.data_loader.load_delaunay_data()
            terrain_data = self.data_loader.load_terrain_data()
            
            # 获取目标站点
            target_stations = delaunay_data['target_station'].unique().tolist()
            
            # 参数优化（如果启用）
            if (self.config['optimization_settings']['enable_optimization'] and 
                method in self.config['optimization_settings']['methods_to_optimize']):
                
                self.error_handler.log_info(f"开始优化 {method} 方法参数")
                self._optimize_method_parameters(method, rainfall_data, station_data, target_stations)
            
            # 执行插值和验证
            self._process_flood_event(method, flood_event, rainfall_data, station_data, 
                                    delaunay_data, terrain_data, target_stations)
            
            self.error_handler.log_info(f"完成处理: 方法={method}, 洪水场次={flood_event}")
            
        except Exception as e:
            self.error_handler.handle_error(e, f"运行 {method} 方法处理 {flood_event}")
            raise
    
    def run_single_method_all_floods(self, method: str) -> None:
        """运行单一方法于所有洪水场次"""
        try:
            flood_events = self.data_loader.get_available_flood_events()
            
            for flood_event in flood_events:
                try:
                    self.run_single_method_single_flood(method, flood_event)
                except Exception as e:
                    self.error_handler.handle_error(e, f"处理洪水场次 {flood_event}")
                    continue
            
        except Exception as e:
            self.error_handler.handle_error(e, f"运行 {method} 方法处理所有洪水场次")
            raise
    
    def run_all_methods_single_flood(self, flood_event: str) -> None:
        """运行所有方法于单一洪水场次"""
        try:
            methods = ['IDW', 'Kriging', 'OI', 'PRISM']
            
            for method in methods:
                try:
                    self.run_single_method_single_flood(method, flood_event)
                except Exception as e:
                    self.error_handler.handle_error(e, f"运行方法 {method}")
                    continue
            
        except Exception as e:
            self.error_handler.handle_error(e, f"运行所有方法处理 {flood_event}")
            raise
    
    def run_all_methods_all_floods(self) -> None:
        """运行所有方法于所有洪水场次（批处理模式）"""
        try:
            flood_events = self.data_loader.get_available_flood_events()
            methods = ['IDW', 'Kriging', 'OI', 'PRISM']
            
            all_results = []
            
            for flood_event in flood_events:
                for method in methods:
                    try:
                        self.error_handler.log_info(f"批处理: {method} - {flood_event}")
                        
                        # 这里可以收集结果用于最终的汇总报告
                        self.run_single_method_single_flood(method, flood_event)
                        
                    except Exception as e:
                        self.error_handler.handle_error(e, f"批处理 {method} - {flood_event}")
                        continue
            
            # 生成汇总报告
            self._generate_batch_summary_report()
            
        except Exception as e:
            self.error_handler.handle_error(e, "批处理模式")
            raise
    
    def _optimize_method_parameters(self, method: str, rainfall_data, station_data, target_stations) -> None:
        """优化方法参数"""
        try:
            # 使用第一个时间步的数据进行优化
            first_timestamp = rainfall_data.index[0]
            station_values = rainfall_data.loc[first_timestamp].values
            
            # 获取站点坐标
            station_coords = station_data[['经度', '纬度']].values
            station_ids = station_data['站点编号'].tolist()
            
            # 创建优化器
            optimizer = SCEUAOptimizer(self.config, self.error_handler)
            
            # 设置时间限制
            start_time = time.time()
            
            def timeout_handler(signum, frame):
                raise TimeoutError("参数优化超时")
            
            signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(self.max_optimization_time)
            
            try:
                # 执行优化
                result = optimizer.optimize_method_parameters(
                    self.interpolators[method], method,
                    station_coords, station_values, station_ids, target_stations
                )
                
                self.error_handler.log_info(f"{method} 参数优化完成")
                
            finally:
                signal.alarm(0)  # 取消定时器
            
        except TimeoutError:
            self.error_handler.log_warning(f"{method} 参数优化超时，使用默认参数")
        except Exception as e:
            self.error_handler.log_warning(f"{method} 参数优化失败，使用默认参数: {str(e)}")
    
    def _process_flood_event(self, method: str, flood_event: str, rainfall_data,
                           station_data, delaunay_data, terrain_data, target_stations) -> None:
        """处理单个洪水事件"""
        try:
            interpolator = self.interpolators[method]

            # 获取站点坐标和ID
            station_coords = station_data[['经度', '纬度']].values
            station_ids = station_data['站点编号'].tolist()

            # 准备栅格输出
            mask_data, mask_profile = terrain_data['mask']

            if self.config['run_settings']['generate_raster_output']:
                # 创建目标网格坐标
                grid_x, grid_y = self.raster_io.create_grid_coordinates(mask_profile)
                target_coords = np.column_stack([grid_x.flatten(), grid_y.flatten()])

                # 处理每个时间步
                for timestamp in rainfall_data.index:
                    try:
                        # 设置时间限制
                        start_time = time.time()

                        def timeout_handler(signum, frame):
                            raise TimeoutError("栅格插值超时")

                        signal.signal(signal.SIGALRM, timeout_handler)
                        signal.alarm(self.max_raster_time)

                        try:
                            station_values = rainfall_data.loc[timestamp].values

                            # 检查是否全零
                            if np.all(station_values == 0):
                                result_grid = np.zeros(target_coords.shape[0])
                            else:
                                # 执行插值
                                result_grid = interpolator.interpolate(
                                    station_coords, station_values, target_coords, mask_data
                                )

                            # 重塑为栅格形状
                            result_raster = result_grid.reshape(mask_data.shape)

                            # 应用掩膜
                            result_raster = self.raster_io.apply_mask(result_raster, mask_data)

                            # 保存栅格
                            output_profile = self.raster_io.create_profile_from_mask(mask_profile)
                            self.raster_io.save_interpolation_result(
                                result_raster, output_profile, method, flood_event, timestamp
                            )

                        finally:
                            signal.alarm(0)  # 取消定时器

                    except TimeoutError:
                        self.error_handler.log_warning(f"时间步 {timestamp} 栅格插值超时，跳过")
                        continue
                    except Exception as e:
                        self.error_handler.log_warning(f"时间步 {timestamp} 处理失败: {str(e)}")
                        continue

            # 执行交叉验证
            self._perform_cross_validation(method, flood_event, rainfall_data,
                                         station_coords, station_ids, target_stations)

        except Exception as e:
            raise InterpolationError(f"处理洪水事件 {flood_event} 失败: {str(e)}")

    def _perform_cross_validation(self, method: str, flood_event: str, rainfall_data,
                                station_coords, station_ids, target_stations) -> None:
        """执行交叉验证"""
        try:
            interpolator = self.interpolators[method]

            # 使用所有时间步的平均值进行验证
            mean_values = rainfall_data.mean(axis=0).values

            # 执行留一法交叉验证
            validation_result = self.validator.leave_one_out_validation(
                interpolator, station_coords, mean_values, station_ids, target_stations
            )

            # 保存验证结果
            self._save_validation_results(method, flood_event, validation_result)

            # 记录验证指标
            metrics = validation_result['metrics']
            self.error_handler.log_info(
                f"{method}-{flood_event} 验证结果: "
                f"RMSE={metrics['RMSE']:.4f}, MAE={metrics['MAE']:.4f}, "
                f"R2={metrics['R2']:.4f}, NSE={metrics['NSE']:.4f}"
            )

        except Exception as e:
            self.error_handler.log_warning(f"交叉验证失败: {str(e)}")

    def _save_validation_results(self, method: str, flood_event: str, validation_result) -> None:
        """保存验证结果"""
        try:
            output_dir = self.raster_io.get_output_directory(method, flood_event)

            # 保存详细验证结果
            validation_df = self.validator.create_validation_report(
                validation_result, method, flood_event
            )

            validation_file = output_dir / f"validation_results_{method}_{flood_event}.csv"
            validation_df.to_csv(validation_file, index=False, encoding='utf-8')

            self.error_handler.log_info(f"验证结果已保存: {validation_file}")

        except Exception as e:
            self.error_handler.log_warning(f"保存验证结果失败: {str(e)}")
    
    def _generate_batch_summary_report(self) -> None:
        """生成批处理汇总报告"""
        try:
            self.error_handler.log_info("生成批处理汇总报告")

            # 收集所有验证结果
            base_dir = Path(self.config['project_paths']['base_dir'])
            output_base = base_dir / "output"

            all_results = []

            # 遍历所有输出目录
            for method_dir in output_base.iterdir():
                if not method_dir.is_dir():
                    continue

                method_name = method_dir.name

                for flood_dir in method_dir.iterdir():
                    if not flood_dir.is_dir():
                        continue

                    flood_event = flood_dir.name

                    # 查找验证结果文件
                    validation_file = flood_dir / f"validation_results_{method_name}_{flood_event}.csv"

                    if validation_file.exists():
                        try:
                            df = pd.read_csv(validation_file, encoding='utf-8')

                            # 提取汇总行
                            summary_row = df[df['station_id'] == 'SUMMARY']

                            if not summary_row.empty:
                                result_row = {
                                    'FloodEvent': flood_event,
                                    'Method': method_name,
                                    'RMSE': summary_row['RMSE'].iloc[0] if 'RMSE' in summary_row.columns else np.nan,
                                    'MAE': summary_row['MAE'].iloc[0] if 'MAE' in summary_row.columns else np.nan,
                                    'R2': summary_row['R2'].iloc[0] if 'R2' in summary_row.columns else np.nan,
                                    'NSE': summary_row['NSE'].iloc[0] if 'NSE' in summary_row.columns else np.nan,
                                    'Count': summary_row['Count'].iloc[0] if 'Count' in summary_row.columns else 0
                                }
                                all_results.append(result_row)

                        except Exception as e:
                            self.error_handler.log_warning(f"读取验证文件失败 {validation_file}: {str(e)}")
                            continue

            if all_results:
                # 创建汇总DataFrame
                summary_df = pd.DataFrame(all_results)

                # 保存汇总报告
                summary_file = output_base / "evaluation_summary.csv"
                summary_df.to_csv(summary_file, index=False, encoding='utf-8')

                self.error_handler.log_info(f"批处理汇总报告已保存: {summary_file}")

                # 打印汇总统计
                self._print_summary_statistics(summary_df)

            else:
                self.error_handler.log_warning("未找到任何验证结果文件")

        except Exception as e:
            self.error_handler.log_warning(f"生成汇总报告失败: {str(e)}")

    def _print_summary_statistics(self, summary_df) -> None:
        """打印汇总统计信息"""
        try:
            print("\n" + "="*80)
            print("批处理汇总统计")
            print("="*80)

            # 按方法分组统计
            method_stats = summary_df.groupby('Method').agg({
                'RMSE': ['mean', 'std', 'min', 'max'],
                'MAE': ['mean', 'std', 'min', 'max'],
                'R2': ['mean', 'std', 'min', 'max'],
                'NSE': ['mean', 'std', 'min', 'max']
            }).round(4)

            print("\n各方法性能统计:")
            print(method_stats)

            # 最佳性能
            print("\n最佳性能:")
            best_rmse = summary_df.loc[summary_df['RMSE'].idxmin()]
            best_nse = summary_df.loc[summary_df['NSE'].idxmax()]

            print(f"最低RMSE: {best_rmse['Method']} - {best_rmse['FloodEvent']} (RMSE: {best_rmse['RMSE']:.4f})")
            print(f"最高NSE: {best_nse['Method']} - {best_nse['FloodEvent']} (NSE: {best_nse['NSE']:.4f})")

            print("="*80)

        except Exception as e:
            self.error_handler.log_warning(f"打印汇总统计失败: {str(e)}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='降雨空间插值系统')
    parser.add_argument('--method', type=str, default='all',
                       choices=['IDW', 'Kriging', 'OI', 'PRISM', 'all'],
                       help='要运行的插值方法')
    parser.add_argument('--flood', type=str, default='all',
                       help='要处理的洪水场次（如 2009-1）或 all 表示所有')
    parser.add_argument('--config', type=str, 
                       default='config/config.yml',
                       help='配置文件路径')
    
    args = parser.parse_args()
    
    try:
        # 创建系统实例
        system = InterpolationSystem(args.config)
        
        # 根据参数选择运行模式
        if args.method == 'all' and args.flood == 'all':
            system.run_all_methods_all_floods()
        elif args.method == 'all':
            system.run_all_methods_single_flood(args.flood)
        elif args.flood == 'all':
            system.run_single_method_all_floods(args.method)
        else:
            system.run_single_method_single_flood(args.method, args.flood)
        
        print("系统运行完成！")
        
    except Exception as e:
        print(f"系统运行失败: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()
