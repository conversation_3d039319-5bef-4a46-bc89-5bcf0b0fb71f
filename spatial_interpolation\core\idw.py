"""
降雨空间插值系统 - IDW插值算法

反距离权重法（Inverse Distance Weighting）的实现。

作者: Augment Agent
日期: 2025-06-26
"""

import numpy as np
from scipy.spatial.distance import cdist
from typing import Tuple, Optional, List
import multiprocessing as mp
from functools import partial

from ..utils.error_handler import InterpolationError, ErrorHandler


class IDWInterpolator:
    """反距离权重插值器"""
    
    def __init__(self, config: dict, error_handler: Optional[ErrorHandler] = None):
        self.config = config
        self.error_handler = error_handler or ErrorHandler()
        self.power = config['method_params']['IDW']['power']
        self.cpu_cores = config['run_settings']['cpu_cores']
    
    def interpolate(self, station_coords: np.ndarray, station_values: np.ndarray,
                   target_coords: np.ndarray, mask: Optional[np.ndarray] = None) -> np.ndarray:
        """
        执行IDW插值
        
        参数:
            station_coords: 站点坐标 (N, 2)
            station_values: 站点值 (N,)
            target_coords: 目标点坐标 (M, 2)
            mask: 掩膜数组，用于标识有效区域
            
        返回:
            插值结果数组 (M,)
        """
        try:
            # 检查输入数据
            self._validate_inputs(station_coords, station_values, target_coords)
            
            # 检查是否所有站点值都为零
            if np.all(station_values == 0):
                self.error_handler.log_info("所有站点值为零，直接返回零值栅格")
                return np.zeros(len(target_coords))
            
            # 过滤零值站点（如果需要）
            valid_mask = station_values > 0
            if not valid_mask.any():
                return np.zeros(len(target_coords))
            
            if not valid_mask.all():
                station_coords = station_coords[valid_mask]
                station_values = station_values[valid_mask]
                self.error_handler.log_info(f"过滤零值站点，剩余 {len(station_values)} 个有效站点")
            
            # 执行插值
            if self.cpu_cores > 1 and len(target_coords) > 1000:
                result = self._interpolate_parallel(station_coords, station_values, target_coords)
            else:
                result = self._interpolate_serial(station_coords, station_values, target_coords)
            
            # 应用掩膜
            if mask is not None:
                result = self._apply_mask(result, mask, target_coords)
            
            return result
            
        except Exception as e:
            raise InterpolationError(f"IDW插值失败: {str(e)}")
    
    def _validate_inputs(self, station_coords: np.ndarray, station_values: np.ndarray,
                        target_coords: np.ndarray) -> None:
        """验证输入数据"""
        if len(station_coords) == 0:
            raise InterpolationError("站点坐标为空")
        
        if len(station_values) == 0:
            raise InterpolationError("站点值为空")
        
        if len(station_coords) != len(station_values):
            raise InterpolationError("站点坐标和值的数量不匹配")
        
        if len(target_coords) == 0:
            raise InterpolationError("目标坐标为空")
        
        if station_coords.shape[1] != 2 or target_coords.shape[1] != 2:
            raise InterpolationError("坐标数据必须是2维的")
    
    def _interpolate_serial(self, station_coords: np.ndarray, station_values: np.ndarray,
                           target_coords: np.ndarray) -> np.ndarray:
        """串行插值计算"""
        try:
            # 计算距离矩阵
            distances = cdist(target_coords, station_coords)
            
            # 处理零距离（目标点与站点重合）
            zero_dist_mask = distances == 0
            
            result = np.zeros(len(target_coords))
            
            for i in range(len(target_coords)):
                if zero_dist_mask[i].any():
                    # 如果目标点与某个站点重合，直接使用该站点的值
                    station_idx = np.where(zero_dist_mask[i])[0][0]
                    result[i] = station_values[station_idx]
                else:
                    # 计算权重
                    weights = 1.0 / (distances[i] ** self.power)
                    weights_sum = np.sum(weights)
                    
                    if weights_sum > 0:
                        result[i] = np.sum(weights * station_values) / weights_sum
                    else:
                        result[i] = np.mean(station_values)
            
            return result
            
        except Exception as e:
            raise InterpolationError(f"串行IDW计算失败: {str(e)}")
    
    def _interpolate_parallel(self, station_coords: np.ndarray, station_values: np.ndarray,
                             target_coords: np.ndarray) -> np.ndarray:
        """并行插值计算"""
        try:
            # 分割目标坐标
            chunk_size = max(1, len(target_coords) // self.cpu_cores)
            chunks = [target_coords[i:i+chunk_size] for i in range(0, len(target_coords), chunk_size)]
            
            # 创建部分函数
            interpolate_chunk = partial(
                self._interpolate_chunk,
                station_coords=station_coords,
                station_values=station_values
            )
            
            # 并行处理
            with mp.Pool(processes=self.cpu_cores) as pool:
                results = pool.map(interpolate_chunk, chunks)
            
            # 合并结果
            return np.concatenate(results)
            
        except Exception as e:
            raise InterpolationError(f"并行IDW计算失败: {str(e)}")
    
    def _interpolate_chunk(self, target_chunk: np.ndarray, station_coords: np.ndarray,
                          station_values: np.ndarray) -> np.ndarray:
        """处理单个数据块的插值"""
        try:
            distances = cdist(target_chunk, station_coords)
            zero_dist_mask = distances == 0
            
            result = np.zeros(len(target_chunk))
            
            for i in range(len(target_chunk)):
                if zero_dist_mask[i].any():
                    station_idx = np.where(zero_dist_mask[i])[0][0]
                    result[i] = station_values[station_idx]
                else:
                    weights = 1.0 / (distances[i] ** self.power)
                    weights_sum = np.sum(weights)
                    
                    if weights_sum > 0:
                        result[i] = np.sum(weights * station_values) / weights_sum
                    else:
                        result[i] = np.mean(station_values)
            
            return result
            
        except Exception as e:
            raise InterpolationError(f"IDW块处理失败: {str(e)}")
    
    def _apply_mask(self, result: np.ndarray, mask: np.ndarray, 
                   target_coords: np.ndarray) -> np.ndarray:
        """应用掩膜到插值结果"""
        try:
            # 这里需要根据target_coords和mask的对应关系来应用掩膜
            # 假设target_coords是按栅格顺序排列的
            if len(result) == mask.size:
                mask_flat = mask.flatten()
                result[mask_flat == 0] = self.config['grid_settings']['nodata_value']
            
            return result
            
        except Exception as e:
            raise InterpolationError(f"应用掩膜失败: {str(e)}")
    
    def get_method_name(self) -> str:
        """获取方法名称"""
        return "IDW"
    
    def get_parameters(self) -> dict:
        """获取当前参数"""
        return {
            'power': self.power,
            'cpu_cores': self.cpu_cores
        }
    
    def set_parameters(self, **params) -> None:
        """设置参数"""
        if 'power' in params:
            self.power = params['power']
            self.config['method_params']['IDW']['power'] = self.power
