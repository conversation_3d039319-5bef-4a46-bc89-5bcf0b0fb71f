"""
降雨空间插值系统 - Kriging插值算法

克里金插值法的实现，包括普通克里金和指示克里金。

作者: Augment Agent
日期: 2025-06-26
"""

import numpy as np
from typing import Tuple, Optional, List
import warnings

try:
    from pykrige.ok import OrdinaryKriging
    from pykrige.uk import UniversalKriging
    PYKRIGE_AVAILABLE = True
except ImportError:
    PYKRIGE_AVAILABLE = False
    warnings.warn("PyKrige未安装，Kriging功能将不可用")

from ..utils.error_handler import InterpolationError, ErrorHandler


class KrigingInterpolator:
    """克里金插值器"""
    
    def __init__(self, config: dict, error_handler: Optional[ErrorHandler] = None):
        if not PYKRIGE_AVAILABLE:
            raise InterpolationError("PyKrige库未安装，无法使用Kriging插值")
        
        self.config = config
        self.error_handler = error_handler or ErrorHandler()
        
        # 获取配置参数
        kriging_config = config['method_params']['Kriging']
        self.variogram_model = kriging_config['variogram_model']
        self.variogram_parameters = kriging_config['variogram_parameters']
        self.anisotropy_scaling = kriging_config['anisotropy_scaling']
        self.anisotropy_angle = kriging_config['anisotropy_angle']
        self.zero_handling = kriging_config.get('zero_handling', 'standard')
        self.zero_threshold = kriging_config.get('zero_threshold', 0.7)
        
        # 错误处理设置
        self.use_pseudo_inverse = config['error_handling']['use_pseudo_inverse']
    
    def interpolate(self, station_coords: np.ndarray, station_values: np.ndarray,
                   target_coords: np.ndarray, mask: Optional[np.ndarray] = None) -> np.ndarray:
        """
        执行Kriging插值
        
        参数:
            station_coords: 站点坐标 (N, 2)
            station_values: 站点值 (N,)
            target_coords: 目标点坐标 (M, 2)
            mask: 掩膜数组
            
        返回:
            插值结果数组 (M,)
        """
        try:
            # 检查输入数据
            self._validate_inputs(station_coords, station_values, target_coords)
            
            # 检查零值情况
            zero_ratio = np.sum(station_values == 0) / len(station_values)
            
            if np.all(station_values == 0):
                self.error_handler.log_info("所有站点值为零，直接返回零值栅格")
                return np.zeros(len(target_coords))
            
            # 根据零值比例选择处理策略
            if zero_ratio > self.zero_threshold and self.zero_handling == 'indicator':
                self.error_handler.log_info(f"零值比例 {zero_ratio:.2f} 超过阈值，使用指示克里金")
                return self._indicator_kriging(station_coords, station_values, target_coords, mask)
            elif self.zero_handling == 'transform' and zero_ratio > 0:
                self.error_handler.log_info("使用变换克里金处理零值")
                return self._transform_kriging(station_coords, station_values, target_coords, mask)
            else:
                return self._ordinary_kriging(station_coords, station_values, target_coords, mask)
            
        except Exception as e:
            raise InterpolationError(f"Kriging插值失败: {str(e)}")
    
    def _validate_inputs(self, station_coords: np.ndarray, station_values: np.ndarray,
                        target_coords: np.ndarray) -> None:
        """验证输入数据"""
        if len(station_coords) == 0:
            raise InterpolationError("站点坐标为空")
        
        if len(station_values) == 0:
            raise InterpolationError("站点值为空")
        
        if len(station_coords) != len(station_values):
            raise InterpolationError("站点坐标和值的数量不匹配")
        
        if len(target_coords) == 0:
            raise InterpolationError("目标坐标为空")
        
        if station_coords.shape[1] != 2 or target_coords.shape[1] != 2:
            raise InterpolationError("坐标数据必须是2维的")
        
        # 检查重复坐标
        unique_coords = np.unique(station_coords, axis=0)
        if len(unique_coords) < len(station_coords):
            self.error_handler.log_warning("发现重复坐标的站点，可能导致奇异矩阵问题")
    
    def _ordinary_kriging(self, station_coords: np.ndarray, station_values: np.ndarray,
                         target_coords: np.ndarray, mask: Optional[np.ndarray] = None) -> np.ndarray:
        """普通克里金插值"""
        try:
            # 过滤负值和异常值
            valid_mask = (station_values >= 0) & np.isfinite(station_values)
            if not valid_mask.all():
                station_coords = station_coords[valid_mask]
                station_values = station_values[valid_mask]
                self.error_handler.log_info(f"过滤异常值，剩余 {len(station_values)} 个有效站点")
            
            if len(station_values) < 3:
                raise InterpolationError("有效站点数量不足，无法进行克里金插值")
            
            # 创建OrdinaryKriging对象
            OK = OrdinaryKriging(
                x=station_coords[:, 0],
                y=station_coords[:, 1],
                z=station_values,
                variogram_model=self.variogram_model,
                variogram_parameters=self.variogram_parameters,
                anisotropy_scaling=self.anisotropy_scaling,
                anisotropy_angle=self.anisotropy_angle,
                verbose=False,
                enable_plotting=False,
                pseudo_inv=self.use_pseudo_inverse
            )
            
            # 执行插值
            if len(target_coords) > 10000:
                # 对于大量目标点，分批处理
                return self._kriging_batch_interpolate(OK, target_coords, mask)
            else:
                z_pred, z_var = OK.execute('points', target_coords[:, 0], target_coords[:, 1])
                
                # 应用掩膜
                if mask is not None:
                    z_pred = self._apply_mask(z_pred, mask)
                
                return z_pred
            
        except Exception as e:
            raise InterpolationError(f"普通克里金计算失败: {str(e)}")
    
    def _indicator_kriging(self, station_coords: np.ndarray, station_values: np.ndarray,
                          target_coords: np.ndarray, mask: Optional[np.ndarray] = None) -> np.ndarray:
        """指示克里金插值"""
        try:
            # 第一步：预测降雨发生概率
            indicator_values = (station_values > 0).astype(float)
            
            OK_indicator = OrdinaryKriging(
                x=station_coords[:, 0],
                y=station_coords[:, 1],
                z=indicator_values,
                variogram_model='spherical',  # 指示克里金通常使用球状模型
                verbose=False,
                enable_plotting=False,
                pseudo_inv=self.use_pseudo_inverse
            )
            
            prob_pred, _ = OK_indicator.execute('points', target_coords[:, 0], target_coords[:, 1])
            prob_pred = np.clip(prob_pred, 0, 1)  # 确保概率在[0,1]范围内
            
            # 第二步：对非零值进行条件克里金
            nonzero_mask = station_values > 0
            if nonzero_mask.sum() < 3:
                # 如果非零站点太少，使用简单平均
                nonzero_mean = np.mean(station_values[nonzero_mask])
                result = prob_pred * nonzero_mean
            else:
                nonzero_coords = station_coords[nonzero_mask]
                nonzero_values = station_values[nonzero_mask]
                
                OK_conditional = OrdinaryKriging(
                    x=nonzero_coords[:, 0],
                    y=nonzero_coords[:, 1],
                    z=nonzero_values,
                    variogram_model=self.variogram_model,
                    variogram_parameters=self.variogram_parameters,
                    verbose=False,
                    enable_plotting=False,
                    pseudo_inv=self.use_pseudo_inverse
                )
                
                cond_pred, _ = OK_conditional.execute('points', target_coords[:, 0], target_coords[:, 1])
                cond_pred = np.maximum(cond_pred, 0)  # 确保非负
                
                # 组合结果
                result = prob_pred * cond_pred
            
            # 应用掩膜
            if mask is not None:
                result = self._apply_mask(result, mask)
            
            return result
            
        except Exception as e:
            raise InterpolationError(f"指示克里金计算失败: {str(e)}")
    
    def _transform_kriging(self, station_coords: np.ndarray, station_values: np.ndarray,
                          target_coords: np.ndarray, mask: Optional[np.ndarray] = None) -> np.ndarray:
        """变换克里金插值"""
        try:
            # 对非零值进行Box-Cox变换或对数变换
            nonzero_mask = station_values > 0
            
            if nonzero_mask.sum() < 3:
                raise InterpolationError("非零站点数量不足，无法进行变换克里金")
            
            # 使用对数变换
            transformed_values = np.log(station_values[nonzero_mask] + 1e-6)  # 加小常数避免log(0)
            
            OK = OrdinaryKriging(
                x=station_coords[nonzero_mask, 0],
                y=station_coords[nonzero_mask, 1],
                z=transformed_values,
                variogram_model=self.variogram_model,
                verbose=False,
                enable_plotting=False,
                pseudo_inv=self.use_pseudo_inverse
            )
            
            # 执行插值
            z_pred_log, _ = OK.execute('points', target_coords[:, 0], target_coords[:, 1])
            
            # 反变换
            z_pred = np.exp(z_pred_log) - 1e-6
            z_pred = np.maximum(z_pred, 0)  # 确保非负
            
            # 应用掩膜
            if mask is not None:
                z_pred = self._apply_mask(z_pred, mask)
            
            return z_pred
            
        except Exception as e:
            raise InterpolationError(f"变换克里金计算失败: {str(e)}")
    
    def _kriging_batch_interpolate(self, OK, target_coords: np.ndarray, 
                                  mask: Optional[np.ndarray] = None) -> np.ndarray:
        """分批进行克里金插值"""
        try:
            batch_size = 5000
            results = []
            
            for i in range(0, len(target_coords), batch_size):
                end_idx = min(i + batch_size, len(target_coords))
                batch_coords = target_coords[i:end_idx]
                
                z_pred, _ = OK.execute('points', batch_coords[:, 0], batch_coords[:, 1])
                results.append(z_pred)
            
            result = np.concatenate(results)
            
            # 应用掩膜
            if mask is not None:
                result = self._apply_mask(result, mask)
            
            return result
            
        except Exception as e:
            raise InterpolationError(f"分批克里金插值失败: {str(e)}")
    
    def _apply_mask(self, result: np.ndarray, mask: np.ndarray) -> np.ndarray:
        """应用掩膜到插值结果"""
        try:
            if len(result) == mask.size:
                mask_flat = mask.flatten()
                result[mask_flat == 0] = self.config['grid_settings']['nodata_value']
            
            return result
            
        except Exception as e:
            raise InterpolationError(f"应用掩膜失败: {str(e)}")
    
    def get_method_name(self) -> str:
        """获取方法名称"""
        return "Kriging"
    
    def get_parameters(self) -> dict:
        """获取当前参数"""
        return {
            'variogram_model': self.variogram_model,
            'variogram_parameters': self.variogram_parameters,
            'anisotropy_scaling': self.anisotropy_scaling,
            'anisotropy_angle': self.anisotropy_angle,
            'zero_handling': self.zero_handling,
            'zero_threshold': self.zero_threshold
        }
    
    def set_parameters(self, **params) -> None:
        """设置参数"""
        if 'variogram_parameters' in params:
            self.variogram_parameters = params['variogram_parameters']
        if 'variogram_model' in params:
            self.variogram_model = params['variogram_model']
        if 'anisotropy_scaling' in params:
            self.anisotropy_scaling = params['anisotropy_scaling']
        if 'anisotropy_angle' in params:
            self.anisotropy_angle = params['anisotropy_angle']
