"""
降雨空间插值系统 - 数据加载模块

负责加载和预处理各种输入数据，包括站点信息、降雨数据、地形数据等。

作者: Augment Agent
日期: 2025-06-26
"""

import os
import pandas as pd
import numpy as np
import rasterio
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
import warnings

from .error_handler import DataError, ErrorHandler


class DataLoader:
    """数据加载器类，负责加载和预处理所有输入数据"""
    
    def __init__(self, config: dict, error_handler: Optional[ErrorHandler] = None):
        self.config = config
        self.error_handler = error_handler or ErrorHandler()
        self.base_dir = Path(config['project_paths']['base_dir'])
        
        # 缓存加载的数据
        self._terrain_data = {}
        self._station_data = None
        self._delaunay_data = None
    
    def load_station_data(self) -> pd.DataFrame:
        """加载站点信息数据"""
        if self._station_data is not None:
            return self._station_data
        
        try:
            station_file = self.base_dir / self.config['project_paths']['station_file']
            
            if not station_file.exists():
                raise DataError(f"站点文件不存在: {station_file}")
            
            # 读取站点数据，处理中文列名
            self._station_data = pd.read_csv(station_file, encoding='utf-8')
            
            # 检查必要的列是否存在
            required_cols = ['站点编号', '经度', '纬度', 'NAME']
            missing_cols = [col for col in required_cols if col not in self._station_data.columns]
            
            if missing_cols:
                raise DataError(f"站点文件缺少必要列: {missing_cols}")
            
            # 检查坐标数据的有效性
            if self._station_data[['经度', '纬度']].isnull().any().any():
                raise DataError("站点坐标数据包含空值")
            
            # 检查重复坐标
            coord_duplicates = self._station_data.duplicated(subset=['经度', '纬度'])
            if coord_duplicates.any():
                duplicate_stations = self._station_data[coord_duplicates]['站点编号'].tolist()
                self.error_handler.log_warning(f"发现重复坐标的站点: {duplicate_stations}")
            
            self.error_handler.log_info(f"成功加载 {len(self._station_data)} 个站点信息")
            return self._station_data
            
        except Exception as e:
            raise DataError(f"加载站点数据失败: {str(e)}")
    
    def load_delaunay_data(self) -> pd.DataFrame:
        """加载Delaunay三角网分析结果"""
        if self._delaunay_data is not None:
            return self._delaunay_data
        
        try:
            delaunay_file = self.base_dir / self.config['project_paths']['delaunay_molan_file']
            
            if not delaunay_file.exists():
                raise DataError(f"Delaunay文件不存在: {delaunay_file}")
            
            self._delaunay_data = pd.read_csv(delaunay_file, encoding='utf-8')
            
            # 检查必要的列
            required_cols = ['target_station', 'significance_level', 'interp_stations_info']
            missing_cols = [col for col in required_cols if col not in self._delaunay_data.columns]
            
            if missing_cols:
                raise DataError(f"Delaunay文件缺少必要列: {missing_cols}")
            
            self.error_handler.log_info(f"成功加载 {len(self._delaunay_data)} 条Delaunay分析记录")
            return self._delaunay_data
            
        except Exception as e:
            raise DataError(f"加载Delaunay数据失败: {str(e)}")
    
    def load_terrain_data(self) -> Dict[str, Tuple[np.ndarray, dict]]:
        """加载地形数据（DEM、坡度、坡向、掩膜）"""
        if self._terrain_data:
            return self._terrain_data
        
        try:
            terrain_dir = self.base_dir / self.config['project_paths']['terrain_dir']
            
            if not terrain_dir.exists():
                raise DataError(f"地形数据目录不存在: {terrain_dir}")
            
            terrain_files = {
                'dem': 'DEM.asc',
                'slope': 'slope.asc', 
                'aspect': 'aspect.asc',
                'mask': 'mask.asc'
            }
            
            for name, filename in terrain_files.items():
                filepath = terrain_dir / filename
                
                if not filepath.exists():
                    raise DataError(f"地形文件不存在: {filepath}")
                
                with rasterio.open(filepath) as src:
                    data = src.read(1)  # 读取第一个波段
                    profile = src.profile
                    
                    # 检查数据有效性
                    if data.size == 0:
                        raise DataError(f"地形文件为空: {filepath}")
                    
                    self._terrain_data[name] = (data, profile)
            
            # 检查所有栅格的空间一致性
            self._validate_raster_consistency()
            
            self.error_handler.log_info("成功加载所有地形数据")
            return self._terrain_data
            
        except Exception as e:
            raise DataError(f"加载地形数据失败: {str(e)}")
    
    def _validate_raster_consistency(self) -> None:
        """验证所有栅格数据的空间一致性"""
        if not self._terrain_data:
            return
        
        reference_profile = None
        reference_name = None
        
        for name, (data, profile) in self._terrain_data.items():
            if reference_profile is None:
                reference_profile = profile
                reference_name = name
                continue
            
            # 检查关键的空间参数
            spatial_keys = ['width', 'height', 'transform', 'crs']
            
            for key in spatial_keys:
                if profile.get(key) != reference_profile.get(key):
                    raise DataError(
                        f"栅格 {name} 与 {reference_name} 的 {key} 不一致: "
                        f"{profile.get(key)} vs {reference_profile.get(key)}"
                    )
    
    def load_rainfall_data(self, flood_event: str) -> pd.DataFrame:
        """加载指定洪水事件的降雨数据"""
        try:
            input_dir = self.base_dir / self.config['project_paths']['input_dir_template'].format(
                flood_event=flood_event
            )
            
            if not input_dir.exists():
                raise DataError(f"洪水事件目录不存在: {input_dir}")
            
            # 获取所有CSV文件
            csv_files = list(input_dir.glob('*.csv'))
            
            if not csv_files:
                raise DataError(f"洪水事件目录中没有找到CSV文件: {input_dir}")
            
            rainfall_data = {}
            
            for csv_file in csv_files:
                station_id = csv_file.stem  # 文件名作为站点编号
                
                try:
                    df = pd.read_csv(csv_file, encoding='utf-8')
                    
                    # 检查必要的列
                    if '时间' not in df.columns or '雨量' not in df.columns:
                        self.error_handler.log_warning(f"站点 {station_id} 的文件缺少必要列")
                        continue
                    
                    # 转换时间列
                    df['时间'] = pd.to_datetime(df['时间'])
                    
                    # 检查雨量数据
                    df['雨量'] = pd.to_numeric(df['雨量'], errors='coerce')
                    
                    # 设置时间为索引
                    df.set_index('时间', inplace=True)
                    
                    rainfall_data[station_id] = df['雨量']
                    
                except Exception as e:
                    self.error_handler.log_warning(f"加载站点 {station_id} 数据失败: {str(e)}")
                    continue
            
            if not rainfall_data:
                raise DataError(f"洪水事件 {flood_event} 没有有效的降雨数据")
            
            # 合并所有站点数据
            combined_df = pd.DataFrame(rainfall_data)
            
            # 填充缺失值为0（假设缺失表示无降雨）
            combined_df = combined_df.fillna(0)
            
            self.error_handler.log_info(
                f"成功加载洪水事件 {flood_event} 的降雨数据: "
                f"{len(combined_df.columns)} 个站点, {len(combined_df)} 个时间步"
            )
            
            return combined_df
            
        except Exception as e:
            raise DataError(f"加载洪水事件 {flood_event} 的降雨数据失败: {str(e)}")
    
    def get_available_flood_events(self) -> List[str]:
        """获取所有可用的洪水事件列表"""
        try:
            input_base = self.base_dir / "input_another"
            
            if not input_base.exists():
                raise DataError(f"输入目录不存在: {input_base}")
            
            flood_events = []
            for item in input_base.iterdir():
                if item.is_dir():
                    flood_events.append(item.name)
            
            flood_events.sort()
            self.error_handler.log_info(f"发现 {len(flood_events)} 个洪水事件: {flood_events}")
            
            return flood_events
            
        except Exception as e:
            raise DataError(f"获取洪水事件列表失败: {str(e)}")
    
    def get_interpolation_stations(self, flood_event: str, target_station: str) -> List[str]:
        """获取指定目标站点的插值站点列表"""
        try:
            delaunay_data = self.load_delaunay_data()
            
            # 筛选指定洪水事件和目标站点的记录
            mask = delaunay_data['target_station'] == target_station
            
            if not mask.any():
                raise DataError(f"未找到目标站点 {target_station} 的插值信息")
            
            # 获取插值站点信息
            interp_info = delaunay_data[mask]['interp_stations_info'].iloc[0]
            
            # 解析插值站点列表（假设是逗号分隔的字符串）
            if isinstance(interp_info, str):
                interp_stations = [s.strip() for s in interp_info.split(',')]
            else:
                raise DataError(f"插值站点信息格式错误: {interp_info}")
            
            return interp_stations
            
        except Exception as e:
            raise DataError(f"获取插值站点失败: {str(e)}")
