"""
降雨空间插值系统 - 验证模块

实现留一法交叉验证和各种评估指标的计算。

作者: Augment Agent
日期: 2025-06-26
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

from ..utils.error_handler import InterpolationError, ErrorHandler


class EvaluationMetrics:
    """评估指标计算类"""
    
    @staticmethod
    def rmse(observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算均方根误差"""
        return np.sqrt(mean_squared_error(observed, predicted))
    
    @staticmethod
    def mae(observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算平均绝对误差"""
        return mean_absolute_error(observed, predicted)
    
    @staticmethod
    def r2(observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算决定系数"""
        return r2_score(observed, predicted)
    
    @staticmethod
    def nse(observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算纳什效率系数"""
        try:
            numerator = np.sum((observed - predicted) ** 2)
            denominator = np.sum((observed - np.mean(observed)) ** 2)
            
            if denominator == 0:
                return np.nan
            
            return 1 - (numerator / denominator)
            
        except Exception:
            return np.nan
    
    @staticmethod
    def bias(observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算偏差"""
        return np.mean(predicted - observed)
    
    @staticmethod
    def pbias(observed: np.ndarray, predicted: np.ndarray) -> float:
        """计算百分比偏差"""
        try:
            numerator = np.sum(predicted - observed)
            denominator = np.sum(observed)
            
            if denominator == 0:
                return np.nan
            
            return (numerator / denominator) * 100
            
        except Exception:
            return np.nan
    
    @staticmethod
    def calculate_all_metrics(observed: np.ndarray, predicted: np.ndarray) -> Dict[str, float]:
        """计算所有评估指标"""
        # 过滤有效数据
        valid_mask = np.isfinite(observed) & np.isfinite(predicted)
        
        if not valid_mask.any():
            return {
                'RMSE': np.nan,
                'MAE': np.nan,
                'R2': np.nan,
                'NSE': np.nan,
                'Bias': np.nan,
                'PBIAS': np.nan,
                'Count': 0
            }
        
        obs_valid = observed[valid_mask]
        pred_valid = predicted[valid_mask]
        
        return {
            'RMSE': EvaluationMetrics.rmse(obs_valid, pred_valid),
            'MAE': EvaluationMetrics.mae(obs_valid, pred_valid),
            'R2': EvaluationMetrics.r2(obs_valid, pred_valid),
            'NSE': EvaluationMetrics.nse(obs_valid, pred_valid),
            'Bias': EvaluationMetrics.bias(obs_valid, pred_valid),
            'PBIAS': EvaluationMetrics.pbias(obs_valid, pred_valid),
            'Count': len(obs_valid)
        }


class CrossValidator:
    """交叉验证器"""
    
    def __init__(self, config: dict, error_handler: Optional[ErrorHandler] = None):
        self.config = config
        self.error_handler = error_handler or ErrorHandler()
    
    def leave_one_out_validation(self, interpolator, station_coords: np.ndarray,
                                station_values: np.ndarray, station_ids: List[str],
                                target_stations: List[str]) -> Dict[str, Any]:
        """
        执行留一法交叉验证
        
        参数:
            interpolator: 插值器对象
            station_coords: 所有站点坐标
            station_values: 所有站点值
            station_ids: 所有站点ID
            target_stations: 目标站点ID列表
            
        返回:
            验证结果字典
        """
        try:
            observed_values = []
            predicted_values = []
            validation_details = []
            
            # 创建站点ID到索引的映射
            station_id_to_index = {sid: i for i, sid in enumerate(station_ids)}
            
            for target_station in target_stations:
                if target_station not in station_id_to_index:
                    self.error_handler.log_warning(f"目标站点 {target_station} 不在站点列表中")
                    continue
                
                target_index = station_id_to_index[target_station]
                target_coord = station_coords[target_index:target_index+1]  # 保持2D形状
                target_value = station_values[target_index]
                
                # 创建训练集（排除目标站点）
                train_mask = np.ones(len(station_coords), dtype=bool)
                train_mask[target_index] = False
                
                train_coords = station_coords[train_mask]
                train_values = station_values[train_mask]
                
                # 检查训练集是否足够
                if len(train_coords) < 3:
                    self.error_handler.log_warning(f"站点 {target_station} 的训练集太小，跳过")
                    continue
                
                try:
                    # 执行插值预测
                    predicted_value = interpolator.interpolate(
                        train_coords, train_values, target_coord
                    )[0]
                    
                    observed_values.append(target_value)
                    predicted_values.append(predicted_value)
                    
                    validation_details.append({
                        'station_id': target_station,
                        'observed': target_value,
                        'predicted': predicted_value,
                        'error': predicted_value - target_value,
                        'abs_error': abs(predicted_value - target_value)
                    })
                    
                except Exception as e:
                    self.error_handler.log_warning(f"站点 {target_station} 插值失败: {str(e)}")
                    continue
            
            if not observed_values:
                raise InterpolationError("没有成功的验证结果")
            
            # 计算评估指标
            observed_array = np.array(observed_values)
            predicted_array = np.array(predicted_values)
            
            metrics = EvaluationMetrics.calculate_all_metrics(observed_array, predicted_array)
            
            return {
                'metrics': metrics,
                'details': validation_details,
                'observed': observed_values,
                'predicted': predicted_values
            }
            
        except Exception as e:
            raise InterpolationError(f"留一法交叉验证失败: {str(e)}")
    
    def spatial_weighted_validation(self, interpolator, station_coords: np.ndarray,
                                  station_values: np.ndarray, station_ids: List[str],
                                  target_stations: List[str]) -> Dict[str, Any]:
        """
        执行空间加权验证（用于SCE-UA优化）
        
        参数:
            interpolator: 插值器对象
            station_coords: 所有站点坐标
            station_values: 所有站点值
            station_ids: 所有站点ID
            target_stations: 目标站点ID列表
            
        返回:
            验证结果字典
        """
        try:
            # 执行标准的留一法验证
            validation_result = self.leave_one_out_validation(
                interpolator, station_coords, station_values, station_ids, target_stations
            )
            
            if not validation_result['observed']:
                return validation_result
            
            # 计算空间权重
            spatial_weights = self._calculate_spatial_weights(
                station_coords, station_ids, target_stations
            )
            
            # 计算加权指标
            observed_array = np.array(validation_result['observed'])
            predicted_array = np.array(validation_result['predicted'])
            
            # 加权RMSE
            weighted_rmse = self._weighted_rmse(observed_array, predicted_array, spatial_weights)
            
            # 加权NSE
            weighted_nse = self._weighted_nse(observed_array, predicted_array, spatial_weights)
            
            validation_result['metrics']['Weighted_RMSE'] = weighted_rmse
            validation_result['metrics']['Weighted_NSE'] = weighted_nse
            validation_result['spatial_weights'] = spatial_weights
            
            return validation_result
            
        except Exception as e:
            raise InterpolationError(f"空间加权验证失败: {str(e)}")
    
    def _calculate_spatial_weights(self, station_coords: np.ndarray, 
                                 station_ids: List[str], target_stations: List[str]) -> np.ndarray:
        """计算空间权重"""
        try:
            from scipy.spatial.distance import pdist, squareform
            
            # 创建站点ID到索引的映射
            station_id_to_index = {sid: i for i, sid in enumerate(station_ids)}
            
            # 获取目标站点的坐标
            target_indices = [station_id_to_index[ts] for ts in target_stations 
                            if ts in station_id_to_index]
            
            if not target_indices:
                return np.ones(len(target_stations))
            
            target_coords = station_coords[target_indices]
            
            # 计算站点间距离
            if len(target_coords) > 1:
                distances = pdist(target_coords)
                distance_matrix = squareform(distances)
                
                # 计算每个站点的平均距离（作为稀疏程度的指标）
                mean_distances = np.mean(distance_matrix, axis=1)
                
                # 距离越大，权重越高（稀疏区域权重更高）
                weights = mean_distances / np.sum(mean_distances)
            else:
                weights = np.ones(len(target_coords))
            
            return weights
            
        except Exception as e:
            self.error_handler.log_warning(f"计算空间权重失败，使用均匀权重: {str(e)}")
            return np.ones(len(target_stations))
    
    def _weighted_rmse(self, observed: np.ndarray, predicted: np.ndarray, 
                      weights: np.ndarray) -> float:
        """计算加权RMSE"""
        try:
            if len(weights) != len(observed):
                weights = np.ones(len(observed)) / len(observed)
            
            squared_errors = (observed - predicted) ** 2
            weighted_mse = np.average(squared_errors, weights=weights)
            
            return np.sqrt(weighted_mse)
            
        except Exception:
            return EvaluationMetrics.rmse(observed, predicted)
    
    def _weighted_nse(self, observed: np.ndarray, predicted: np.ndarray, 
                     weights: np.ndarray) -> float:
        """计算加权NSE"""
        try:
            if len(weights) != len(observed):
                weights = np.ones(len(observed)) / len(observed)
            
            mean_observed = np.average(observed, weights=weights)
            
            numerator = np.sum(weights * (observed - predicted) ** 2)
            denominator = np.sum(weights * (observed - mean_observed) ** 2)
            
            if denominator == 0:
                return np.nan
            
            return 1 - (numerator / denominator)
            
        except Exception:
            return EvaluationMetrics.nse(observed, predicted)
    
    def create_validation_report(self, validation_results: Dict[str, Any], 
                               method_name: str, flood_event: str) -> pd.DataFrame:
        """创建验证报告"""
        try:
            details = validation_results['details']
            metrics = validation_results['metrics']
            
            # 创建详细结果DataFrame
            df_details = pd.DataFrame(details)
            
            # 添加汇总信息
            summary_row = {
                'station_id': 'SUMMARY',
                'observed': np.nan,
                'predicted': np.nan,
                'error': np.nan,
                'abs_error': np.nan
            }
            
            # 添加指标列
            for metric_name, metric_value in metrics.items():
                summary_row[metric_name] = metric_value
            
            # 添加方法和事件信息
            df_details['method'] = method_name
            df_details['flood_event'] = flood_event
            
            return df_details
            
        except Exception as e:
            raise InterpolationError(f"创建验证报告失败: {str(e)}")
