# 降雨空间插值系统用户手册

## 系统概述

本系统是一个功能强大、配置灵活且性能卓越的Python降雨空间插值计算框架，集成了四种核心插值方法：

- **IDW (反距离权重法)**: 简单快速的确定性插值方法
- **Kriging (克里金法)**: 基于地统计学的最优线性无偏估计
- **OI (最优插值法)**: 源于气象数据同化的融合方法  
- **PRISM**: 考虑地形影响的混合统计-地理模型

## 主要特性

✅ **四种插值方法**: IDW、Kriging、OI、PRISM  
✅ **自动参数优化**: 基于SCE-UA算法的全局优化  
✅ **并行计算**: 8核CPU并行处理，共享内存优化  
✅ **零值处理**: 指示克里金、变换克里金等高级策略  
✅ **批处理模式**: 支持多洪水场次、多方法批量处理  
✅ **评估指标**: MAE、R2、NSE、RMSE等全面评估  
✅ **配置驱动**: 所有参数通过YAML文件配置  

## 安装指南

### 1. 环境准备

推荐使用conda创建独立环境：

```bash
conda create -n rainfall_interp python=3.9
conda activate rainfall_interp
```

### 2. 依赖安装

**方法一：使用conda（推荐）**
```bash
conda install -c conda-forge pandas numpy rasterio pykrige sceua pyyaml matplotlib scipy scikit-learn
```

**方法二：使用pip**
```bash
pip install -r requirements.txt
```

### 3. 验证安装

```bash
python -c "import numpy, pandas, rasterio, pykrige, sceua; print('安装成功！')"
```

## 数据准备

### 目录结构

确保您的数据按以下结构组织：

```
D:/pythondata/jiangyuchazhi/
├── input_another/
│   ├── 2009-1/          # 洪水场次文件夹
│   │   ├── 3633.csv     # 站点降雨数据
│   │   ├── 3634.csv
│   │   └── ...
│   ├── 2009-2/
│   └── ...
├── terrain/90/
│   ├── DEM.asc          # 数字高程模型
│   ├── aspect.asc       # 坡向
│   ├── mask.asc         # 流域掩膜
│   └── slope.asc        # 坡度
├── stations.csv         # 站点信息
├── Delaunay_Molan.csv   # 插值站点关系
└── spatial_interpolation/  # 系统代码
```

### 数据格式要求

**stations.csv** (UTF-8编码):
```csv
站点编号,经度,纬度,NAME
3633,120.123,30.456,站点A
3634,120.234,30.567,站点B
```

**降雨数据文件** (如3633.csv):
```csv
时间,雨量
2009-01-15 14:00:00,0.5
2009-01-15 15:00:00,1.2
```

**Delaunay_Molan.csv**:
```csv
target_station,significance_level,interp_stations_info
3633,0.05,"3634,3635,3636"
3634,0.05,"3633,3635,3637"
```

## 配置文件说明

系统通过 `config/config.yml` 进行配置。主要配置项：

### 基本设置
```yaml
run_settings:
  methods_to_run: ['all']        # 运行方法: IDW/Kriging/OI/PRISM/all
  floods_to_run: ['all']         # 洪水场次: 具体场次或all
  cpu_cores: 8                   # CPU核心数
  generate_raster_output: true   # 是否生成栅格输出
  max_raster_time: 300          # 栅格插值时间限制(秒)
  max_optimization_time: 1800   # 参数优化时间限制(秒)
```

### 插值方法参数
```yaml
method_params:
  IDW:
    power: 2.0                   # 距离权重指数
  
  Kriging:
    variogram_model: 'spherical' # 变异函数模型
    variogram_parameters: [10.0, 100000.0, 1.0]  # [基台值,变程,块金值]
    zero_handling: 'standard'    # 零值处理: standard/indicator/transform
    zero_threshold: 0.7          # 零值阈值
  
  OI:
    observation_error_variance: 0.5      # 观测误差方差
    background_error_corr_length: 50000.0  # 背景场误差相关长度
  
  PRISM:
    search_radius: 150000.0      # 搜索半径(米)
    min_stations: 5              # 最小站点数
    max_stations: 20             # 最大站点数
    distance_power: 2.0          # 距离权重指数
    elevation_power: 1.0         # 高程权重指数
```

### 参数优化设置
```yaml
optimization_settings:
  enable_optimization: true      # 是否启用优化
  methods_to_optimize: ['Kriging', 'OI', 'PRISM']  # 需要优化的方法
  sceua_params:
    max_iterations: 1000         # 最大迭代次数
    n_complexes: 5               # 复合体数量
    objective_function: 'nse_mean'  # 目标函数: rmse/nse_mean/weighted_rmse
```

## 使用方法

### 基本命令格式

```bash
cd spatial_interpolation
python run.py --method <方法> --flood <洪水场次> [--config <配置文件>]
```

### 四种运行场景

#### 1. 单一方法 + 单一洪水场次
```bash
python run.py --method PRISM --flood 2009-1
```

#### 2. 单一方法 + 所有洪水场次  
```bash
python run.py --method PRISM --flood all
```

#### 3. 所有方法 + 单一洪水场次
```bash
python run.py --method all --flood 2009-1
```

#### 4. 所有方法 + 所有洪水场次（批处理）
```bash
python run.py --method all --flood all
```

### 高级用法

**指定配置文件:**
```bash
python run.py --method Kriging --flood 2009-1 --config my_config.yml
```

**仅进行参数优化（不生成栅格）:**
修改config.yml中的 `generate_raster_output: false`

## 输出结果

### 文件结构
```
output/
├── IDW/2009-1/
│   ├── rainfall_IDW_2009-01-15T14-00-00.asc    # 栅格输出
│   ├── rainfall_IDW_2009-01-15T15-00-00.asc
│   └── validation_results_IDW_2009-1.csv       # 验证结果
├── Kriging/2009-1/
└── evaluation_summary.csv                       # 批处理汇总报告
```

### 验证结果文件

**validation_results_[方法]_[场次].csv**:
```csv
station_id,observed,predicted,error,abs_error,method,flood_event
3633,5.2,4.8,-0.4,0.4,Kriging,2009-1
3634,3.1,3.5,0.4,0.4,Kriging,2009-1
SUMMARY,,,,,Kriging,2009-1,RMSE,MAE,R2,NSE,Count
```

### 批处理汇总报告

**evaluation_summary.csv**:
```csv
FloodEvent,Method,RMSE,MAE,R2,NSE,Count
2009-1,IDW,1.23,0.89,0.85,0.82,15
2009-1,Kriging,1.15,0.82,0.88,0.86,15
2009-1,OI,1.18,0.85,0.87,0.84,15
2009-1,PRISM,1.09,0.78,0.91,0.89,15
```

## 性能优化建议

### 1. 硬件配置
- **CPU**: 8核或更多，支持并行计算
- **内存**: 16GB以上，处理大型栅格数据
- **存储**: SSD硬盘，提高I/O性能

### 2. 参数调优
- **cpu_cores**: 设置为实际CPU核心数
- **max_raster_time**: 根据数据规模调整时间限制
- **generate_raster_output**: 仅在需要时启用栅格输出

### 3. 数据优化
- 使用合适的栅格分辨率
- 预处理去除异常值
- 合理设置搜索半径

## 故障排除

### 常见问题

**1. 导入错误**
```
ImportError: No module named 'pykrige'
```
解决：使用conda安装地理空间库
```bash
conda install -c conda-forge pykrige
```

**2. 内存不足**
```
MemoryError: Unable to allocate array
```
解决：减少栅格分辨率或启用分块处理

**3. 奇异矩阵错误**
```
LinAlgError: Singular matrix
```
解决：检查重复坐标，启用伪逆选项

**4. 文件编码错误**
```
UnicodeDecodeError
```
解决：确保CSV文件使用UTF-8编码

### 调试模式

启用详细错误信息：
```yaml
error_handling:
  verbose_errors: true
```

## 技术支持

如遇问题，请检查：
1. 数据格式是否正确
2. 配置文件语法是否有误
3. 依赖包是否完整安装
4. 系统资源是否充足

## 更新日志

**v1.0.0 (2025-06-26)**
- 初始版本发布
- 支持四种插值方法
- 集成SCE-UA参数优化
- 实现并行计算和批处理
