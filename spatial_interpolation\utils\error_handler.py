"""
降雨空间插值系统 - 错误处理模块

定义了系统中使用的各种自定义异常类。

作者: Augment Agent
日期: 2025-06-26
"""

import logging
import traceback
from typing import Optional, Any


class InterpolationError(Exception):
    """插值过程中的基础异常类"""
    
    def __init__(self, message: str, details: Optional[dict] = None):
        super().__init__(message)
        self.message = message
        self.details = details or {}
        
    def __str__(self):
        if self.details:
            return f"{self.message}. 详细信息: {self.details}"
        return self.message


class DataError(InterpolationError):
    """数据相关的异常"""
    pass


class ConfigError(InterpolationError):
    """配置文件相关的异常"""
    pass


class OptimizationError(InterpolationError):
    """参数优化相关的异常"""
    pass


class RasterError(InterpolationError):
    """栅格处理相关的异常"""
    pass


class ErrorHandler:
    """错误处理器，提供统一的错误处理和日志记录功能"""
    
    def __init__(self, verbose: bool = True):
        self.verbose = verbose
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('spatial_interpolation')
        logger.setLevel(logging.INFO if self.verbose else logging.WARNING)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def handle_error(self, error: Exception, context: str = "") -> None:
        """处理错误并记录日志"""
        error_msg = f"错误发生在 {context}: {str(error)}"
        
        if isinstance(error, InterpolationError):
            self.logger.error(error_msg)
            if self.verbose and error.details:
                self.logger.error(f"错误详情: {error.details}")
        else:
            self.logger.error(error_msg)
            if self.verbose:
                self.logger.error(f"堆栈跟踪:\n{traceback.format_exc()}")
    
    def log_info(self, message: str) -> None:
        """记录信息日志"""
        self.logger.info(message)
    
    def log_warning(self, message: str) -> None:
        """记录警告日志"""
        self.logger.warning(message)
    
    def log_debug(self, message: str) -> None:
        """记录调试日志"""
        self.logger.debug(message)


def safe_execute(func, *args, error_handler: Optional[ErrorHandler] = None, 
                context: str = "", **kwargs) -> Any:
    """安全执行函数，自动处理异常"""
    try:
        return func(*args, **kwargs)
    except Exception as e:
        if error_handler:
            error_handler.handle_error(e, context)
        raise
