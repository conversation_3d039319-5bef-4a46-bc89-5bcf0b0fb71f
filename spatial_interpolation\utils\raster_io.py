"""
降雨空间插值系统 - 栅格I/O模块

负责栅格数据的读写操作，包括ASC格式的处理。

作者: Augment Agent
日期: 2025-06-26
"""

import os
import numpy as np
import rasterio
from rasterio.transform import from_bounds
from pathlib import Path
from typing import Tuple, Dict, Optional, Union
import datetime

from .error_handler import RasterError, ErrorHandler


class RasterIO:
    """栅格数据输入输出处理类"""
    
    def __init__(self, config: dict, error_handler: Optional[ErrorHandler] = None):
        self.config = config
        self.error_handler = error_handler or ErrorHandler()
        self.nodata_value = config['grid_settings']['nodata_value']
    
    def read_raster(self, filepath: Union[str, Path]) -> Tuple[np.ndarray, dict]:
        """读取栅格文件"""
        try:
            filepath = Path(filepath)
            
            if not filepath.exists():
                raise RasterError(f"栅格文件不存在: {filepath}")
            
            with rasterio.open(filepath) as src:
                data = src.read(1)  # 读取第一个波段
                profile = src.profile
                
                # 检查数据有效性
                if data.size == 0:
                    raise RasterError(f"栅格文件为空: {filepath}")
                
                return data, profile
                
        except Exception as e:
            raise RasterError(f"读取栅格文件失败 {filepath}: {str(e)}")
    
    def write_raster(self, data: np.ndarray, profile: dict, filepath: Union[str, Path]) -> None:
        """写入栅格文件"""
        try:
            filepath = Path(filepath)
            
            # 确保输出目录存在
            filepath.parent.mkdir(parents=True, exist_ok=True)
            
            # 更新profile
            profile_copy = profile.copy()
            profile_copy.update({
                'dtype': data.dtype,
                'count': 1,
                'nodata': self.nodata_value
            })
            
            with rasterio.open(filepath, 'w', **profile_copy) as dst:
                dst.write(data, 1)
                
            self.error_handler.log_info(f"成功写入栅格文件: {filepath}")
            
        except Exception as e:
            raise RasterError(f"写入栅格文件失败 {filepath}: {str(e)}")
    
    def create_grid_coordinates(self, profile: dict) -> Tuple[np.ndarray, np.ndarray]:
        """根据栅格profile创建网格坐标"""
        try:
            transform = profile['transform']
            width = profile['width']
            height = profile['height']
            
            # 创建像元中心坐标
            cols, rows = np.meshgrid(np.arange(width), np.arange(height))
            
            # 转换为地理坐标
            xs, ys = rasterio.transform.xy(transform, rows, cols)
            
            return np.array(xs), np.array(ys)
            
        except Exception as e:
            raise RasterError(f"创建网格坐标失败: {str(e)}")
    
    def get_valid_pixels(self, mask_data: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """获取有效像元的行列索引"""
        try:
            # 假设mask中非零值表示有效区域
            valid_mask = (mask_data != 0) & (mask_data != self.nodata_value)
            rows, cols = np.where(valid_mask)
            
            return rows, cols
            
        except Exception as e:
            raise RasterError(f"获取有效像元失败: {str(e)}")
    
    def apply_mask(self, data: np.ndarray, mask: np.ndarray) -> np.ndarray:
        """对数据应用掩膜"""
        try:
            result = data.copy()
            invalid_mask = (mask == 0) | (mask == self.nodata_value)
            result[invalid_mask] = self.nodata_value
            
            return result
            
        except Exception as e:
            raise RasterError(f"应用掩膜失败: {str(e)}")
    
    def generate_output_filename(self, method: str, flood_event: str, 
                                timestamp: datetime.datetime) -> str:
        """生成输出文件名"""
        try:
            time_format = self.config['output_settings']['time_format']
            time_str = timestamp.strftime(time_format)
            
            # 清理文件名中的特殊字符
            time_str = time_str.replace(':', '-').replace(' ', '_')
            
            filename = f"rainfall_{method}_{time_str}.asc"
            
            return filename
            
        except Exception as e:
            raise RasterError(f"生成输出文件名失败: {str(e)}")
    
    def get_output_directory(self, method: str, flood_event: str) -> Path:
        """获取输出目录路径"""
        try:
            base_dir = Path(self.config['project_paths']['base_dir'])
            output_template = self.config['project_paths']['output_dir_template']
            
            output_dir = base_dir / output_template.format(
                method=method, 
                flood_event=flood_event
            )
            
            # 确保目录存在
            output_dir.mkdir(parents=True, exist_ok=True)
            
            return output_dir
            
        except Exception as e:
            raise RasterError(f"创建输出目录失败: {str(e)}")
    
    def save_interpolation_result(self, data: np.ndarray, profile: dict, 
                                 method: str, flood_event: str, 
                                 timestamp: datetime.datetime) -> Path:
        """保存插值结果"""
        try:
            output_dir = self.get_output_directory(method, flood_event)
            filename = self.generate_output_filename(method, flood_event, timestamp)
            filepath = output_dir / filename
            
            self.write_raster(data, profile, filepath)
            
            return filepath
            
        except Exception as e:
            raise RasterError(f"保存插值结果失败: {str(e)}")
    
    def calculate_raster_statistics(self, data: np.ndarray, mask: Optional[np.ndarray] = None) -> Dict[str, float]:
        """计算栅格统计信息"""
        try:
            if mask is not None:
                valid_data = data[(mask != 0) & (mask != self.nodata_value) & 
                                (data != self.nodata_value)]
            else:
                valid_data = data[data != self.nodata_value]
            
            if len(valid_data) == 0:
                return {
                    'min': np.nan,
                    'max': np.nan,
                    'mean': np.nan,
                    'std': np.nan,
                    'count': 0
                }
            
            stats = {
                'min': float(np.min(valid_data)),
                'max': float(np.max(valid_data)),
                'mean': float(np.mean(valid_data)),
                'std': float(np.std(valid_data)),
                'count': len(valid_data)
            }
            
            return stats
            
        except Exception as e:
            raise RasterError(f"计算栅格统计失败: {str(e)}")
    
    def resample_to_points(self, data: np.ndarray, profile: dict, 
                          points_x: np.ndarray, points_y: np.ndarray) -> np.ndarray:
        """将栅格数据重采样到指定点位置"""
        try:
            from rasterio.transform import rowcol
            
            transform = profile['transform']
            
            # 将地理坐标转换为像元坐标
            rows, cols = rowcol(transform, points_x, points_y)
            
            # 确保坐标在有效范围内
            height, width = data.shape
            valid_mask = (rows >= 0) & (rows < height) & (cols >= 0) & (cols < width)
            
            result = np.full(len(points_x), self.nodata_value, dtype=data.dtype)
            
            if valid_mask.any():
                valid_rows = rows[valid_mask]
                valid_cols = cols[valid_mask]
                result[valid_mask] = data[valid_rows, valid_cols]
            
            return result
            
        except Exception as e:
            raise RasterError(f"栅格重采样失败: {str(e)}")
    
    def create_profile_from_mask(self, mask_profile: dict) -> dict:
        """基于掩膜创建新的栅格profile"""
        try:
            profile = mask_profile.copy()
            profile.update({
                'dtype': 'float32',
                'nodata': self.nodata_value,
                'count': 1
            })
            
            return profile
            
        except Exception as e:
            raise RasterError(f"创建栅格profile失败: {str(e)}")
