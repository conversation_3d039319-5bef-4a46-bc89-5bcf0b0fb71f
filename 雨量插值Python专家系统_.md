

# **复杂地形下降雨空间插值的高性能计算与方法论综合报告**

## **第一部分：系统架构与数据基础**

### **第1节：地形感知降雨插值导论**

#### **1.1 空间降雨估算的挑战**

降雨是水文循环中至关重要的输入变量，其精确的空间分布信息是水文模型模拟、洪水预报和水资源管理成功的基石。然而，降雨本质上是在离散点（雨量站）进行测量的，而水文应用则需要连续的空间降雨场 1。将点测量数据转换为空间连续的栅格数据场的过程，即空间插值，构成了水文学研究的核心挑战之一。这一挑战在地形复杂的山区尤为突出，因为地形的抬升、阻挡和“雨影”效应会导致降雨在极短的距离内发生剧烈变化 2。在这种情况下，简单的插值方法往往无法捕捉到降雨与地形之间的复杂非线性关系，从而导致模拟结果出现显著偏差。

#### **1.2 从确定性模型到地统计与混合模型**

为了应对这一挑战，降雨空间插值技术经历了从简单到复杂的演进。本报告将重点分析和实现四种代表不同技术路线的插值方法，以进行全面的对比研究：

1. **反距离权重法 (Inverse Distance Weighting, IDW):** 作为一种确定性方法，IDW基于“相近的相似”地理学第一定律，根据距离的远近为观测点赋予权重。它计算简单、速度快，常被用作评估更复杂方法的基准。  
2. **克里金法 (Kriging):** 作为地统计学的核心，克里金法不仅考虑了距离，还通过变异函数分析数据的内在空间自相关结构（即变量在不同空间位置上的关联程度），提供“最优线性无偏估计” 3。  
3. **最优插值法 (Optimal Interpolation, OI):** 源于气象学中的数据同化领域，OI通过一个数学框架，将一个先验的“背景场”（如模式预报场）与稀疏的观测数据进行融合，以期得到一个误差最小化的“分析场” 4。  
4. **参数-高程独立坡面回归模型 (PRISM):** 这是一种先进的混合统计-地理模型，它显式地将高程、坡度、坡向等地形因子纳入一个移动窗口的回归分析中，以模拟地形对降雨的物理影响，特别适用于山区 2。

本项目旨在构建一个统一的框架，不仅实现这四种方法，更重要的是，在一个真实世界的流域数据集上，对它们的性能进行严格和公正的比较。

#### **1.3 项目目标与系统设计哲学**

本项目的核心目标是为一位水文领域的研究者，开发一个功能强大、配置灵活且性能卓越的Python计算框架。该框架的设计哲学遵循以下原则：

* **科学严谨性:** 每种插值方法的实现都将严格遵循其核心理论，并综合学术文献中的最佳实践。  
* **自动化与可配置性:** 系统的所有行为，从数据输入路径到模型参数，再到运行流程，都将通过一个中心化的配置文件进行控制，使用户能够轻松地进行实验和调试。  
* **知识驱动插值:** 系统的一个独特之处在于，它将利用用户提供的Delaunay\_molan.csv文件进行动态、事件驱动的插值站点选择。这代表了一种专家知识与算法结合的模式，即插值邻域并非通过通用算法（如最近邻）确定，而是由先验的、针对每次洪水事件的空间相关性分析结果来指导。  
* **高性能计算:** 考虑到现代水文数据的高时空分辨率，系统在设计之初就将性能作为核心考量。通过利用多核CPU并行计算和高效的内存管理策略，确保系统能够处理大规模数据集而不会崩溃。  
* **透明与可学习性:** 本系统旨在成为一个“白盒”，而非一个难以理解的“黑盒”。通过详尽的文档、清晰的代码结构和丰富的注释，用户不仅能使用该工具，更能从中学习到高级空间插值方法和高性能计算的实现细节。

### **第2节：插值系统：架构与配置**

#### **2.1 模块化软件架构**

为了实现高度的灵活性、可维护性和可扩展性，本系统将采用模块化的软件架构。在用户指定的基础目录（例如 D:/pythondate/jiangyuchazhi/）下，脚本将自动创建以下结构化的项目目录（如果不存在）：

spatial\_interpolation/  
├── config/  
│   └── config.yml  
├── core/  
│   ├── \_\_init\_\_.py  
│   ├── idw.py  
│   ├── kriging.py  
│   ├── oi.py  
│   └── prism.py  
├── utils/  
│   ├── \_\_init\_\_.py  
│   ├── data\_loader.py  
│   ├── raster\_io.py  
│   └── error\_handler.py  
├── optimization/  
│   ├── \_\_init\_\_.py  
│   └── sceua\_optimizer.py  
├── evaluation/  
│   ├── \_\_init\_\_.py  
│   └── validator.py  
└── run.py

* config/: 存放中心配置文件 config.yml。  
* core/: 包含四种核心插值算法的实现模块。每个文件对应一种方法，封装其理论和计算逻辑。  
* utils/: 存放通用辅助函数。data\_loader.py 负责所有数据的读取和预处理；raster\_io.py 负责栅格文件的读写；error\_handler.py 包含自定义的异常处理类。  
* optimization/: 存放参数优化相关的代码。sceua\_optimizer.py 封装了SCE-UA算法的调用逻辑和目标函数定义。  
* evaluation/: 存放模型评估模块。validator.py 实现了留一法交叉验证（LOOCV）和各项评估指标的计算。  
* run.py: 整个系统的命令行入口，负责解析用户指令，调用其他模块完成插值流程。

#### **2.2 中心配置文件 (config.yml)**

为了满足新手用户对可配置性和易学性的要求，系统将由一个中心化的config.yml文件驱动。选择YAML格式是因为其高度的人类可读性、对注释的良好支持以及能够轻松表示复杂的嵌套数据结构（如模型参数），这优于功能受限的configparser和对人类不甚友好的JSON 7。

下面是config.yml的结构示例，所有参数的详细说明见 **关键表1**。

YAML

\# \--------------------------------------------------------------------------- \#  
\#                      降雨空间插值系统 \- 配置文件  
\# \--------------------------------------------------------------------------- \#

\# 1\. 项目路径设置  
project\_paths:  
  base\_dir: "D:/pythondata/jiangyuchazhi/"  
  \# 输入洪水事件文件夹模板, {flood\_event} 将被替换为 '2009-1' 等  
  input\_dir\_template: "input\_another/{flood\_event}"  
  \# 输出文件夹模板, {method} 和 {flood\_event} 将被替换  
  output\_dir\_template: "output/{method}/{flood\_event}"  
  terrain\_dir: "terrain/90/"  
  station\_file: "stations.csv"  
  delaunay\_molan\_file: "Delaunay\_molan.csv"

\# 2\. 运行设置  
run\_settings:  
  \# 要运行的方法列表, 可选项: 'IDW', 'Kriging', 'OI', 'PRISM', 或 'all'  
  methods\_to\_run: \['all'\]  
  \# 要运行的洪水场次列表, 或 'all' 表示遍历所有  
  floods\_to\_run: \['all'\]  
  \# 使用的CPU核心数  
  cpu\_cores: 8  
  \# 是否生成每个时刻的流域面雨量栅格输出  
  generate\_raster\_output: true

\# 3\. 栅格设置 (将从mask.asc自动读取，此处为备用)  
grid\_settings:  
  \# 这些值将从mask.asc文件自动推断, 无需手动设置  
  \# nrows:   
  \# ncols:   
  \# xllcorner:   
  \# yllcorner:   
  \# cellsize:   
  nodata\_value: \-9999

\# 4\. 各插值方法参数  
method\_params:  
  IDW:  
    power: 2.0  
  Kriging:  
    \# 可选: 'linear', 'power', 'gaussian', 'spherical', 'exponential'  
    variogram\_model: 'spherical'  
    \# 如果不进行优化，将使用这里的参数。格式为 \[sill, range, nugget\]  
    \# 如果进行优化，这里的值可以作为优化的初始猜测  
    variogram\_parameters: \[10.0, 100000.0, 1.0\]  
    \# 各向异性参数  
    anisotropy\_scaling: 1.0  
    anisotropy\_angle: 0.0  
  OI:  
    \# 观测误差方差 (sigma\_r^2)  
    observation\_error\_variance: 0.5  
    \# 背景场误差相关长度 (米)  
    background\_error\_corr\_length: 50000.0  
  PRISM:  
    \# 搜索半径 (米)  
    search\_radius: 150000.0  
    \# 最小和最大邻近站点数  
    min\_stations: 5  
    max\_stations: 20  
    \# 距离权重指数  
    distance\_power: 2.0  
    \# 高程权重指数  
    elevation\_power: 1.0

\# 5\. 参数优化设置  
optimization\_settings:  
  \# 是否启用参数优化  
  enable\_optimization: true  
  optimizer: 'SCE-UA'  
  \# 需要优化的方法列表  
  methods\_to\_optimize:  
  \# SCE-UA 算法参数  
  sceua\_params:  
    max\_iterations: 1000  
    n\_complexes: 5  
    \# 交叉验证的目标函数, 可选: 'rmse', 'nse'  
    objective\_function: 'rmse'  
  \# 各方法参数的优化搜索边界  
  optimization\_bounds:  
    Kriging:  
      \# sill, range, nugget  
      variogram\_parameters: \[, , \[0.1, 5\]\]  
    OI:  
      \# observation\_error\_variance, background\_error\_corr\_length  
      params: \[\[0.1, 2.0\], \]  
    PRISM:  
      \# search\_radius, distance\_power, elevation\_power  
      params: \[, \[1.0, 3.0\], \[0.5, 2.0\]\]

#### **2.3 命令行接口 (CLI)**

为了方便用户以批处理方式运行不同的实验场景，系统提供了一个基于run.py的命令行接口。我们选择Python标准库中的argparse模块来实现此功能。尽管click等第三方库提供了更现代的API 10，但

argparse无需额外安装，功能强大，完全能够满足本项目定义的运行需求，是构建健壮科学计算工具的可靠选择 12。

该接口将覆盖用户在需求\#17中明确提出的所有四种运行场景：

1. **运行单一方法于单一洪水场次:**  
   Bash  
   python run.py \--method PRISM \--flood 2009-1

2. **运行单一方法于所有洪水场次:**  
   Bash  
   python run.py \--method PRISM \--flood all

3. **运行所有方法于单一洪水场次:**  
   Bash  
   python run.py \--method all \--flood 2009-1

4. **运行所有方法于所有洪水场次 (批处理模式):**  
   Bash  
   python run.py \--method all \--flood all

run.py脚本将首先加载config.yml文件，然后用命令行参数覆盖run\_settings中的相应配置，从而实现灵活的运行控制。

#### **关键表1: config.yml 参数参考**

| 参数路径 (YAML) | 参数名 | 数据类型 | 描述 | 示例值 |
| :---- | :---- | :---- | :---- | :---- |
| project\_paths.base\_dir | 基础目录 | string | 项目的根目录路径。 | "D:/pythondata/jiangyuchazhi/" |
| project\_paths.input\_dir\_template | 输入目录模板 | string | 包含点雨量CSV文件的文件夹路径模板。 | "input\_another/{flood\_event}" |
| project\_paths.output\_dir\_template | 输出目录模板 | string | 输出文件（栅格和报告）的路径模板。 | "output/{method}/{flood\_event}" |
| project\_paths.terrain\_dir | 地形数据目录 | string | 存放DEM、坡度、坡向和掩膜栅格文件的目录。 | "terrain/90/" |
| project\_paths.station\_file | 站点信息文件 | string | 包含所有站点元数据（经纬度等）的CSV文件名。 | "stations.csv" |
| project\_paths.delaunay\_molan\_file | 动态站点选择文件 | string | 定义每个目标站点在各洪水场次中应使用的插值站点的CSV文件名。 | "Delaunay\_molan.csv" |
| run\_settings.methods\_to\_run | 运行方法 | list/string | 要执行的插值方法列表，或 'all' 表示全部。 | \`\` |
| run\_settings.floods\_to\_run | 运行洪水场次 | list/string | 要处理的洪水场次文件夹名称列表，或 'all' 表示全部。 | \['2009-1', '2009-2'\] |
| run\_settings.cpu\_cores | CPU核心数 | integer | 用于并行计算的CPU核心数量。 | 8 |
| run\_settings.generate\_raster\_output | 生成栅格输出 | boolean | 是否为每个时间步长生成并保存插值后的栅格文件（.asc）。 | true |
| grid\_settings.nodata\_value | 无效值 | float/int | 输出栅格中用于表示无数据区域的值。 | \-9999 |
| method\_params.IDW.power | IDW权重指数 | float | 反距离权重的幂指数p。 | 2.0 |
| method\_params.Kriging.variogram\_model | 克里金变异函数模型 | string | 使用的理论变异函数模型。 | 'spherical' |
| method\_params.Kriging.variogram\_parameters | 克里金变异函数参数 | list | 模型的参数 \[基台值, 变程, 块金值\]。 | \[10.0, 100000.0, 1.0\] |
| method\_params.Kriging.anisotropy\_scaling | 各向异性缩放 | float | Y方向上的拉伸因子，用于处理各向异性。 | 1.0 |
| method\_params.Kriging.anisotropy\_angle | 各向异性角度 | float | 坐标系逆时针旋转的角度（度）。 | 0.0 |
| method\_params.OI.observation\_error\_variance | OI观测误差方差 | float | 观测数据（雨量站）的误差方差 (σr2​)。 | 0.5 |
| method\_params.OI.background\_error\_corr\_length | OI背景场误差相关长度 | float | 背景场误差的空间相关长度L（米）。 | 50000.0 |
| method\_params.PRISM.search\_radius | PRISM搜索半径 | float | PRISM模型选择邻近站点的搜索半径（米）。 | 150000.0 |
| optimization\_settings.enable\_optimization | 启用优化 | boolean | 是否对PRISM、Kriging、OI方法进行参数自动优化。 | true |
| optimization\_settings.sceua\_params.max\_iterations | SCE-UA最大迭代次数 | integer | SCE-UA算法的最大迭代次数。 | 1000 |
| optimization\_settings.sceua\_params.n\_complexes | SCE-UA复合体数量 | integer | SCE-UA算法中的复合体（complexes）数量。 | 5 |

### **第3节：数据管道：从原始文件到分析就绪集**

#### **3.1 加载地理空间基础数据**

构建任何空间插值模型的第一步是建立一个坚实的地理空间数据基础。

* **地形栅格数据:** 本系统使用rasterio库来处理所有栅格数据，包括数字高程模型（DEM.asc）、坡度（slope.asc）、坡向（aspect.asc）和流域掩膜（mask.asc）。选择rasterio是因为它提供了简洁、Pythonic的API，并与NumPy紧密集成，便于进行数值计算 14。在加载时，脚本会读取每个栅格文件的元数据（如坐标参考系统CRS、仿射变换参数、维度等）和数值矩阵本身 16。这些元数据，特别是  
  mask.asc的元数据，将用于定义所有输出栅格的地理范围和分辨率，确保空间上的一致性。  
* **站点元数据:** 位于stations.csv文件中的站点信息是连接雨量数据和地理空间位置的桥梁。系统将使用pandas库读取此文件，并特别注意处理UTF-8编码和中文字段名（“站点编号”、“经度”、“纬度”、“NAME”），以确保数据的正确解析。

#### **3.2 处理时序降雨数据**

系统被设计用于处理多个独立的洪水事件，每个事件的数据存放在单独的文件夹中（例如，input\_another/2009-1/）。数据处理管道将按以下步骤操作：

1. **遍历事件:** 根据config.yml中的floods\_to\_run设置，脚本将遍历指定的洪水事件文件夹。  
2. **读取点雨量:** 在每个事件文件夹内，脚本会读取所有的CSV文件。每个文件代表一个雨量站（文件名即站点编号），包含“时间”和“雨量”两列。  
3. **构建统一数据集:** 所有站点的时序数据将被合并成一个单一的pandas DataFrame。该DataFrame的索引是时间戳，列是站点编号，值是对应的雨量。这种“宽格式”数据结构极大地简化了后续在特定时间点提取所有站点雨量值的操作，是进行时空分析的标准预处理步骤。

#### **3.3 实现动态、事件特定的站点选择**

这是本系统最核心和最具创新性的功能之一，它体现了将领域专家知识融入自动化工作流的思想。传统的插值方法通常采用通用的邻域搜索策略，例如选择最近的N个点或一个固定半径内的所有点。然而，用户提供的Delaunay\_molan.csv文件蕴含了更为复杂的先验知识：对于每一次特定的洪水事件，哪些站点对于插值某个特定目标站点是最优的，已经被预先计算出来。这可能源于对每次风暴路径、强度和空间结构的深入分析。本系统必须严格遵循这一专家指导。

**实现逻辑:**

1. **加载指导文件:** 系统启动时，将Delaunay\_molan.csv加载到一个pandas DataFrame中。  
2. **事件过滤:** 在处理每一个洪水事件（如'2009-1'）时，首先根据事件名称过滤该DataFrame，得到仅与当前事件相关的站点关系。  
3. **确定插值任务:** 过滤后的target\_station列定义了在该事件中需要被插值的站点集合。这组站点将成为后续留一法交叉验证（LOOCV）的目标集。  
4. **动态邻域查找:** 在插值循环中，当需要计算某个target\_station在特定时刻的值时，系统不再进行空间搜索。而是直接在过滤后的指导文件中，查找该target\_station对应的interp\_stations\_info列。该列包含了一个明确的站点编号列表，这些站点就是用于本次插值的唯一输入源。  
5. **集成到核心算法:** 这个“查找替代搜索”的机制将被无缝集成到IDW、Kriging、OI和PRISM四种方法的核心函数中。这确保了所有插值计算都严格遵循用户的先验空间分析结果，使模型更具针对性和潜在的更高精度。

#### **3.4 处理数据质量与特殊值**

在处理高频（如小时尺度）降雨数据时，一个普遍存在且极易被忽视的问题是数据中存在大量的零值。这不仅是一个数据特征，更是一个对统计插值方法构成严峻挑战的工程和理论问题。

对零值问题的深刻认识与主动策略:  
一个新手级的实现可能会直接将含有零值的数据投入模型，这对于Kriging和OI等依赖方差和协方差的统计模型来说，可能导致奇异矩阵、模型拟合失败或产生物理意义不明确的负值。高级的解决方法，如文献中提到的零膨胀伽马（ZAGA）分布模型，虽然理论上更优，但实现复杂 18。本系统采取一种兼顾科学严谨性和工程实用性的两阶段主动策略，以体现对这一问题的专业处理能力：

1. **预计算检查:** 在对任何一个时间步长进行插值之前，系统会首先检查该时刻所有相关输入站点（由Delaunay\_molan.csv确定）的雨量值。  
2. **全零情景 (All-Zero Condition):** 如果所有输入站点的雨量值均为0，则无需启动任何复杂的插值算法。这是一个物理上明确的信号，表明该区域在该小时内极有可能没有降雨。系统将直接生成一个所有像元值均为0的栅格。这个简单的分流操作有两大好处：  
   * **大幅提升性能:** 避免了数以万计的无意义的、耗时的插值计算。  
   * **增强鲁棒性:** 从根本上避免了将全零数据输入统计模型可能引发的数值不稳定问题。  
3. **混合值情景 (Mixed-Value Condition):** 如果输入站点中既有零值也有非零值，数据将被正常传递给选定的插值函数。在本报告的后续章节（如第5.4节）中，将讨论这种混合值数据对Kriging等方法理论假设的挑战，并指出这可能是模型误差的一个来源。

这种主动的、分情况处理的策略，不仅解决了用户对内存和性能的担忧（需求\#12, \#16），也体现了对降雨数据特性的深刻理解，是从业余脚本到专业级科学计算软件的关键区别。

## **第二部分：核心插值方法论：理论与实现**

### **第4节：反距离权重法 (IDW): 一个确定性基准**

#### **4.1 理论基础**

反距离权重法（IDW）是一种简单而直观的确定性空间插值方法。其核心假设是，待插值点的值更接近于其附近已知点的值，而距离较远的点对其影响较小。该方法通过对邻近的已知点值进行加权平均来估算未知点的值，权重与距离的倒数成正比 19。

IDW的数学表达式为：

Z^(x0​)=∑i=1n​wi​∑i=1n​wi​Z(xi​)​

其中，Z^(x0​) 是在位置 x0​ 的估计值，Z(xi​) 是在已知位置 xi​ 的观测值，n 是用于插值的邻近点数量。权重 wi​ 的计算公式为：

wi​=dip​1​

这里，di​ 是待插值点 x0​ 与已知点 xi​ 之间的距离。参数 p 是一个正实数，称为幂指数或功率参数。p 的值决定了权重随距离增加而衰减的速度。较高的 p 值意味着更强调最近点的影响，生成的插值表面细节更丰富但可能不平滑；较小的 p 值则给予较远点更大的影响，产生更平滑、更平均的表面 20。通常，  
p 的默认值为2 22。

#### **4.2 Python 实现**

在我们的系统中，IDW的实现将是一个独立的Python函数，位于core/idw.py模块中。该函数将接收以下参数：

* interp\_points\_coords: 一个NumPy数组，包含根据Delaunay\_molan.csv选择的插值站点的经纬度坐标。  
* interp\_points\_values: 一个NumPy数组，包含这些站点在当前时间步的降雨量值。  
* target\_grid\_coords: 一个NumPy数组，包含输出栅格所有像元的中心坐标。  
* power: 从config.yml中读取的幂指数 p。

函数内部将利用NumPy和SciPy进行高效的距离计算和加权平均，以生成最终的插值栅格矩阵。

#### **4.3 优势与局限性**

**优势:**

* **简单快速:** IDW算法逻辑清晰，易于理解和实现，计算速度非常快，使其成为一个理想的基准方法 23。  
* **直观:** 其核心思想符合地理学第一定律，具有很强的直观性。

**局限性:**

* **“牛眼”效应:** 在采样点周围容易产生同心圆状的“牛眼”伪影，特别是在采样点稀疏的区域。  
* **无法外推:** IDW的插值结果永远不会超过或低于输入样本点的最大值和最小值，因此无法创建数据中未曾出现过的山峰或山谷 21。  
* **对数据分布敏感:** 当采样点聚集或分布不均时，插值结果可能会产生偏差 24。  
* **各向同性假设:** IDW假设空间影响是各向同性的，即在所有方向上影响都只与距离有关，无法捕捉方向性的变化趋势（如盛行风向对污染物扩散的影响）24。

尽管存在这些局限性，IDW仍然因其简单性和速度而在许多应用中被广泛使用，并为评估更复杂方法的性能提供了一个重要的参考标准。

### **第5节：基于地统计学的克里金插值**

#### **5.1 区域化变量理论**

克里金法是地统计学的核心，它建立在区域化变量理论之上。该理论认为，空间分布的变量（如降雨量）既具有随机性，又具有结构性。这种结构性，即空间自相关性，意味着彼此靠近的样本点比相距较远的点更相似 3。克里金法通过一个名为

**变异函数**（variogram）的工具来定量描述这种空间自相关结构 25。

实验变异函数 γ∗(h) 定义为相距为 h 的所有点对的观测值之差的平方和的平均值的一半：

γ∗(h)=2N(h)1​i=1∑N(h)​\[Z(xi​)−Z(xi​+h)\]2

其中，N(h) 是距离为 h 的点对数量，Z(xi​) 和 Z(xi​+h) 是点对的观测值。

#### **5.2 变异函数模型**

通过计算所有样本点对的实验变异函数值，可以得到一个散点图。地统计分析的关键一步是选择一个理论变异函数模型来拟合这些散点，从而得到一个连续的函数来描述所有距离上的空间变异性。一个典型的理论变异函数模型（如图所示）由三个关键参数定义 26：

* **块金值 (Nugget, C0​):** 当距离 h 趋近于0时，变异函数的值。它代表了测量误差和在极小尺度上无法解释的空间变异性。  
* **基台值 (Sill, C0​+C):** 变异函数随距离增加而达到的平台值，代表了区域化变量的总方差。  
* **变程 (Range, a):** 变异函数达到基台值时的距离。它表示了空间自相关的范围，超过这个距离的点被认为在统计上是相互独立的。

常见的理论模型包括球状模型（Spherical）、指数模型（Exponential）和高斯模型（Gaussian）。模型的选择和参数的拟合是克里金插值质量的关键。

#### **5.3 使用PyKrige的实现**

本系统将采用PyKrige库来实现克里金插值。PyKrige是一个成熟、功能全面且在地球科学领域广泛应用的Python库 28。

**实现步骤:**

1. **实例化:** 在core/kriging.py中，将创建一个OrdinaryKriging类的实例。构造函数需要输入动态选择的插值站点的x坐标、y坐标和雨量值。  
   Python  
   from pykrige.ok import OrdinaryKriging

   OK \= OrdinaryKriging(  
       x=station\_coords\[:, 0\],  
       y=station\_coords\[:, 1\],  
       z=station\_rain\_values,  
       variogram\_model=config\['method\_params'\]\['Kriging'\]\['variogram\_model'\],  
       variogram\_parameters=config\['method\_params'\]\['Kriging'\]\['variogram\_parameters'\],  
       anisotropy\_scaling=config\['method\_params'\]\['Kriging'\]\['anisotropy\_scaling'\],  
       anisotropy\_angle=config\['method\_params'\]\['Kriging'\]\['anisotropy\_angle'\],  
       verbose=False,  
       enable\_plotting=False  
   )

2. **参数配置:** 变异函数模型（variogram\_model）及其参数（variogram\_parameters）将从config.yml文件中读取。如果用户在配置中启用了参数优化，这些参数将被SCE-UA算法（详见第8节）自动寻找和设定。  
3. **各向异性处理:** PyKrige支持各向异性，即空间自相关性在不同方向上存在差异。通过配置anisotropy\_scaling（缩放因子）和anisotropy\_angle（旋转角度），可以对坐标系进行调整，以适应这种方向性依赖 31。这些参数同样可以被纳入优化过程。  
4. **执行插值:** 调用execute方法，传入目标栅格的网格坐标，即可得到插值结果矩阵和每个点的克里金方差（估计误差）矩阵。  
   Python  
   kriged\_grid, variance\_grid \= OK.execute('grid', grid\_x, grid\_y)

#### **5.4 应对零降雨挑战**

如第3.4节所述，本系统通过预检查和分流处理全零时间步，已经规避了最极端的情况。然而，当输入数据是零值和非零值的混合体时，克里金法面临更严峻的挑战：

**零膨胀数据的问题：**
1. **变异函数拟合困难：** 大量零值会导致变异函数在短距离内出现异常波动，影响理论模型的拟合质量。
2. **平稳性假设违背：** 零值的空间聚集性破坏了克里金法要求的二阶平稳性假设。
3. **负值预测：** 普通克里金可能产生物理上不合理的负降雨预测值。

**改进策略：**
- **指示克里金（Indicator Kriging）：** 对于零值占比超过70%的时段，系统将自动启用指示克里金，先预测降雨发生概率，再结合条件期望估算降雨量。
- **零膨胀模型：** 对于极端零膨胀情况，可选择启用零膨胀伽马（ZAGA）分布模型进行建模。
- **变换克里金：** 对非零值进行Box-Cox变换后再进行克里金插值，最后反变换得到最终结果。

这些高级方法将在config.yml中作为可选项提供，用户可根据数据特征选择最适合的处理策略。

### **第6节：基于数据同化的最优插值 (OI)**

#### **6.1 最优插值理论**

最优插值（Optimal Interpolation, OI）是一种源自气象和海洋学数据同化领域的技术 4。其核心思想是将一个不完美的、但空间连续的“背景场”（first guess or background field,

xb）与一组稀疏但相对准确的“观测值”（observations, yo）进行线性组合，从而生成一个在统计意义上最优的“分析场”（analysis field, xa） 34。

其核心方程可以表示为：

xa=xb+W(yo−Hxb)

其中：

* xa 是最终的分析场（即我们的插值结果）。  
* xb 是背景场（先验估计）。  
* yo 是观测值向量（雨量站的实际读数）。  
* H 是观测算子，它将模型状态（背景场）从格点空间映射到观测空间。在我们的案例中，它是一个简单的插值操作，用于从背景场栅格中提取对应于雨量站位置的值。  
* (yo−Hxb) 被称为“新息”（innovation），代表了观测与背景场估计之间的差异。  
* W 是权重矩阵，也称为“增益矩阵”（gain matrix）。它的计算是为了最小化分析场的误差方差。

这个最优的权重矩阵 W 由背景场误差协方差矩阵 B 和观测误差协方差矩阵 R 共同决定：

W=BHT(HBHT+R)−1

这里的 B 和 R 的准确估计是OI方法成功的关键 5。

#### **6.2 在缺少预报模型下的OI实现策略**

在经典的天气预报应用中，xb 通常是数值天气预报模型（NWP）的短期预报结果，而 B 则是通过集合预报或历史预报误差统计得到的 5。然而，在本项目中，我们没有可用的NWP模型。这是一个典型的挑战，但通过构建一个自洽的（self-consistent）流程，我们可以从现有数据中估算所需的全部要素。这体现了将理论方法灵活应用于实际数据限制的专业能力。

**分步实现逻辑：**

1. **生成背景场 (xb):** 对于每一个需要插值的时间步，我们首先使用一种计算成本低廉且鲁棒的方法，即**反距离权重法 (IDW)**，利用该时刻所有可用的雨量站数据生成一个覆盖整个研究区域的连续降雨栅格。这个IDW插值结果就作为我们该时刻的背景场 xb。这个选择是基于IDW的快速和简单性，能够提供一个合理的初步空间估计 19。  
2. **估计观测误差协方差矩阵 (R):** 矩阵 R 描述了观测本身的误差。它主要由两部分构成：仪器误差和代表性误差（即点测量值与栅格平均值之间的差异）36。  
   * **对角元素:** R 的对角元素 Rii​=σr2​ 是单个观测的误差方差。这个值可以根据雨量计的已知精度设定为一个常数，并在config.yml中进行配置。这是一个重要的可调参数。  
   * **非对角元素:** 在大多数OI应用中，假设不同雨量站的观测误差是相互独立的，因此非对角元素 Rij​ (for i=j) 被设为0 4。本系统将遵循这一标准简化假设。  
3. **估计背景场误差协方差矩阵 (B):** 矩阵 B 描述了我们自生成的背景场（IDW插值场）的误差特性。这是本实现中最具创造性的一步。  
   * **误差估计:** 首先，我们计算背景场在每个雨量站位置的误差。对于第 i 个雨量站，其背景场误差 ei​=ZIDW​(xi​)−Zobs​(xi​)，其中 ZIDW​(xi​) 是IDW背景场在站 i 位置的值，Zobs​(xi​) 是站 i 的实际观测值。  
   * **方差（对角元素）:** 背景场误差的总体方差 σb2​ 可以通过计算所有站点上 ei​ 的方差得到。这就是 B 矩阵的对角元素。  
   * 协方差（非对角元素）: 背景场误差在空间上是相关的。我们可以假设这种相关性随距离衰减。一个常用的模型是指数衰减模型。因此，B 矩阵的非对角元素 Bij​ 可以被建模为：  
     Bij​=σb2​exp(−Ldij​​)

     其中，dij​ 是站点 i 和 j 之间的距离，L 是一个“相关长度”参数，描述了误差相关性衰减的特征尺度。这个 L 和前面提到的 σr2​ 都是OI模型的关键参数，非常适合通过SCE-UA优化算法进行自动校准。

#### **6.3 Python 实现**

core/oi.py 模块将负责编排上述三步流程。它将首先调用IDW模块生成背景场，然后根据配置或优化得到的参数构建 B 和 R 矩阵，最后对每个栅格点求解OI方程，生成最终的分析场。由于涉及到矩阵求逆，需要特别注意处理可能出现的奇异矩阵问题（详见第10.2节）。

### **第7节：PRISM模型：一种对地形敏感的方法**

#### **7.1 概念框架**

PRISM（Parameter-elevation Regressions on Independent Slopes Model）是一种独特的“混合统计-地理”模型，专门为绘制复杂地形下的气候要素（特别是降雨和温度）而设计 1。与纯粹依赖距离或统计相关性的方法不同，PRISM的核心思想是，在局部尺度上，气候要素（如降雨）与高程存在线性关系。PRISM通过一个移动的窗口，为每一个目标栅格单元（pixel）动态地建立一个局部的、考虑了地形影响的降雨-高程回归方程，并用此方程来预测该栅格的降雨量。

#### **7.2 核心算法：分步解析**

PRISM的插值过程是逐个栅格进行的。对于研究区内的每一个栅格单元，算法执行以下步骤 37：

1. **定义局部窗口 (Define Local Window):** 以当前目标栅格为中心，定义一个搜索半径。所有落入此半径内的雨量站都被选为潜在的邻近站点，用于构建回归模型。这个半径是一个关键的可调参数。  
2. **计算站点权重 (Calculate Station Weights):** 这是PRISM最复杂也最精妙的部分。它不是对所有邻近站点一视同仁，而是根据一系列物理和地理因素为每个站点计算一个综合权重 W。这个权重决定了该站点在后续回归分析中的影响力。根据文献 37，综合权重  
   W 的计算公式如下：  
   W=Wc​×Wd​×Wz​×Wf​×Wp​×Wl​×Wt​×We​

   **注意：** 根据原始PRISM文献，权重公式应为各分量权重的乘积形式，而非复杂的平方根组合。这确保了各权重因子的独立性和可解释性。

   其中，每个分量权重都有其特定的地理意义：  
   * Wd​ (**距离权重**): 距离目标栅格越近的站点权重越高。通常采用反距离平方的形式。  
   * Wz​ (**高程权重**): 高程与目标栅格越接近的站点权重越高。  
   * Wc​ (**聚类权重**): 用于降低空间上聚集在一起的站点群的整体影响力，避免其在回归中被过度代表。如果多个站点靠得很近，它们的 Wc​ 会被调低。  
   * Wf​ (**地形坡面权重**): 这是PRISM的关键。模型会判断每个站点和目标栅格是否位于同一个大的地形坡面（由坡度和坡向定义）上。位于同一坡面的站点权重最高，因为它们最有可能共享相同的局地气候机制（如迎风坡或背风坡）。  
   * Wp​,Wl​,Wt​,We​: 这些是更高级的权重，分别代表海岸邻近度、垂直大气分层、地形位置（山谷/山脊）和有效地形。在本项目中，我们将重点实现最核心的 Wd​,Wz​,Wc​,Wf​，因为它们可以直接从用户提供的DEM、坡度和坡向数据中计算得出。  
3. 执行加权线性回归 (Perform Weighted Linear Regression): 使用上一步计算出的综合权重 W，对选定的邻近站点进行加权线性回归分析，建立降雨量（Precipitation）与高程（Elevation）之间的局部关系：  
   Precipitation=β1​×Elevation+β0​

   其中，回归系数 β1​（坡度）和 β0​（截距）是通过加权最小二乘法确定的。  
4. **预测栅格值 (Predict Grid Cell Value):** 最后，将目标栅格自身的高程值（从DEM中提取）代入上一步得到的局部回归方程，计算出该栅格的最终降雨估算值。

这个过程在每个栅格上重复进行，因此PRISM能够捕捉到随地理位置变化的降雨-高程关系，从而精细地绘制出受地形强烈影响的降雨分布图。

#### **7.3 Python 实现**

core/prism.py 模块将实现这一复杂的移动窗口回归算法。

* 它将大量使用rasterio和numpy来处理地形栅格数据。  
* 核心是一个循环，遍历掩膜（mask）范围内的所有有效栅格。  
* 在循环内部，将实现上述的四个步骤，特别是复杂的权重计算函数。  
* 为了性能，邻近站点的搜索可以使用scipy.spatial.KDTree来加速。

#### **7.4 参数化以供优化**

PRISM模型的性能高度依赖于其内部的一系列超参数。为了实现自动化校准，以下关键参数将被提取到config.yml中，并作为SCE-UA优化算法的目标（详见第8节）：

* **搜索半径 (search\_radius):** 决定了局部回归窗口的大小。  
* **最小/最大站点数 (min\_stations, max\_stations):** 限制了参与回归的站点数量。  
* **权重函数中的幂指数:** 如距离权重 Wd​ 和高程权重 Wz​ 中的幂指数，它们控制了这些因素的相对重要性。

通过对这些参数进行优化，可以使PRISM模型更好地适应特定流域的地形和气候特征。

## **第三部分：高级功能与性能优化**

### **第8节：利用SCE-UA算法进行自动校准**

#### **8.1 SCE-UA算法简介**

在复杂环境模型的参数校准中，目标函数（如误差函数）的响应面往往是崎岖不平、非线性和多峰的。传统的局部优化算法容易陷入局部最优解，而无法找到全局最优参数集。SCE-UA（Shuffled Complex Evolution \- University of Arizona）算法是一种强大的全局优化算法，专门为解决这类问题而设计 38。

SCE-UA的核心思想是\*\*“竞争性演化”**和**“复合体洗牌”\*\*的结合 38：

1. **初始化:** 在整个参数可行域内随机生成一个初始种群。  
2. **分组:** 将种群按适应度（目标函数值）排序，并分成若干个“复合体”（complexes）。  
3. **演化 (Exploitation):** 在每个复合体内，独立地使用Nelder-Mead单纯形法（Simplex Method）进行局部搜索，使复合体向更优的参数空间区域“演化”。  
4. **洗牌 (Exploration):** 经过一定次数的演化后，将所有复合体中的点合并，重新排序，然后再次分组。这个“洗牌”过程实现了不同复合体之间的信息共享，允许搜索跳出局部最优，从而实现对整个参数空间的有效探索。

这种探索与利用的平衡机制，使得SCE-UA在水文模型校准等领域被证明是一种非常鲁棒和高效的全局优化方法 41。

#### **8.2 集成sceua Python包**

本系统将利用现有的、高质量的sceua Python包来实现SCE-UA算法 43。该包提供了与

scipy.optimize.minimize类似的接口，易于集成 43。

**集成流程:**

1. **定义目标函数 (Objective Function):** 参数优化的核心是定义一个目标函数，该函数接收一组候选参数，并返回一个标量值来评价这组参数的优劣。在本项目中，目标函数将执行一个完整的留一法交叉验证（LOOCV）流程：  
   * 函数输入：一个包含待优化参数值的NumPy数组（例如，对于Kriging，可能是\[sill, range, nugget\]）。  
   * 函数过程：  
     a. 遍历Delaunay\_molan.csv中为当前洪水事件指定的所有target\_station。  
     b. 对于每个target\_station，使用候选参数配置插值模型（如Kriging）。  
     c. 使用其对应的interp\_stations\_info站点数据，预测该target\_station位置的降雨量。  
     d. 计算预测值与实际观测值之间的误差。  
   * 函数返回：**改进的目标函数设计** - 为避免优化偏向站点密集区域，系统提供两种目标函数选项：
     - **NSE平均值：** 计算所有target_station的NSE值的平均值，避免RMSE总和对站点数量的敏感性。
     - **空间加权RMSE：** 根据站点的空间分布密度对RMSE进行加权，稀疏区域的站点获得更高权重。
     用户可在config.yml中的optimization_settings.objective_function中选择'nse_mean'或'weighted_rmse'。
2. **设定参数边界 (Parameter Bounds):** 每种方法的待优化参数都需要一个合理的搜索范围。这些边界将在config.yml的optimization\_bounds部分定义，并传递给sceua.minimize函数。  
3. **调用优化器:** 当enable\_optimization为true时，主程序将为config.yml中指定的每种方法（如'Kriging', 'OI', 'PRISM'）调用sceua.minimize，传入目标函数和参数边界。优化器返回的最优参数集result.x将用于该方法的最终插值计算。

#### **8.3 各方法的优化参数**

**关键表2: 高级方法的可优化参数**

| 插值方法 | 优化参数 | 物理/统计意义 | 配置文件路径 (optimization\_bounds) |
| :---- | :---- | :---- | :---- |
| **Kriging** | variogram\_parameters (sill, range, nugget) | 基台值（总方差），变程（空间相关性距离），块金值（测量误差/微尺度变异）32 | Kriging.variogram\_parameters |
|  | anisotropy\_scaling, anisotropy\_angle | 各向异性缩放比例和旋转角度，用于修正方向性依赖 31。 | （可作为扩展添加） |
| **OI** | observation\_error\_variance | 观测误差的方差 (σr2​)，反映雨量计测量的不确定性。 | OI.params |
|  | background\_error\_corr\_length | 背景场误差的相关长度 (L)，决定了背景场误差在空间中衰减的速度。 | OI.params |
| **PRISM** | search\_radius | 局部回归窗口的搜索半径。 | PRISM.params |
|  | distance\_power | 距离权重函数的幂指数。 | PRISM.params |
|  | elevation\_power | 高程权重函数的幂指数。 | PRISM.params |

通过自动化校准这些关键参数，系统能够客观地为每种方法找到在特定流域和洪水事件下的“最佳状态”，从而使得方法间的性能比较更加公平和有意义。这使用户摆脱了繁琐的手动调参过程，并将模型校准提升到了一个科学、可重复的水平。

### **第9节：面向地理空间分析的高性能计算**

#### **9.1 并行计算的必要性**

空间插值，特别是对于高分辨率栅格和长时序数据，是一项计算密集型任务。例如，为一个1000x1000的栅格进行插值，意味着需要执行一百万次独立的计算。对于一个持续数十或数百小时的洪水事件，串行计算的耗时将是无法接受的。用户的8核处理器为并行计算提供了硬件基础，必须充分利用以实现可接受的运行时间，这直接响应了用户的需求\#14。

#### **9.2 使用multiprocessing实现CPU并行化**

本系统将采用Python标准库中的multiprocessing模块来实现CPU并行化。其基本思路是将整个计算任务（如对一个栅格的所有像元进行插值）分解成多个子任务，并将这些子任务分配给不同的CPU核心同时处理。

**实现策略:**

1. **任务分解:** 对于每个时间步的插值，目标是计算输出栅格中每个像元的值。我们将栅格的像元坐标分割成N个块（N等于config.yml中指定的cpu\_cores数）。  
2. **进程池:** 使用multiprocessing.Pool创建一个包含N个工作进程的进程池。  
3. **任务分发:** 使用pool.map或类似函数，将每个像元块的计算任务分发给一个工作进程。每个进程独立完成其负责区域的插值计算。  
4. **结果聚合:** 主进程等待所有工作进程完成后，收集各自的计算结果（插值后的栅格块），并将它们拼接成一个完整的输出栅格。

#### **9.3 通过共享内存优化并行性能**

一个初级的并行实现可能会在每次调用pool.map时，将所有需要的数据（如站点坐标、站点雨量值、DEM栅格、坡度栅格等）通过序列化（pickling）的方式传递给每个工作进程。对于GB级别的地形栅格数据，这种重复的数据复制和传输会产生巨大的内存和时间开销，甚至可能导致系统因内存耗尽而崩溃。这直接关系到用户对内存效率（\#12, \#16）的关切。

为了构建一个真正高性能的系统，必须采用更高级的内存管理策略。这里的关键在于识别数据的变与不变：

* **静态数据:** 在一次完整的洪水事件插值过程中，地形数据（DEM、坡度、坡向）和站点元数据（坐标）是固定不变的。
* **动态数据:** 只有每个时间步的站点雨量值是变化的。

**同步机制说明：**
- **静态数据同步：** 地形数据在进程初始化时一次性加载到共享内存，所有工作进程只读访问，无需同步锁。
- **动态数据传递：** 每个时间步的降雨值通过pool.map的参数直接传递，避免共享内存的写入竞争。
- **内存安全：** 使用multiprocessing.RawArray确保共享内存的原子性访问，防止数据竞争。

基于此，我们将采用**共享内存**技术来避免对大型静态数据的重复复制 44。

**共享内存实现流程:**

1. **主进程加载与创建:** 在主进程中，首先使用rasterio和pandas将所有静态数据（DEM等）加载到NumPy数组中。然后，使用multiprocessing.RawArray或Python 3.8+中更现代的multiprocessing.shared\_memory模块，在内存中创建可以被所有子进程访问的共享内存块。  
2. **数据复制到共享内存:** 将加载的NumPy数组内容一次性地复制到这些共享内存块中。  
3. **工作进程初始化:** 在创建multiprocessing.Pool时，使用initializer参数指定一个初始化函数。这个函数在每个工作进程启动时仅被调用一次。initargs参数则用来将共享内存块的句柄（名称或地址）传递给这个初始化函数。  
4. **工作进程重构数组:** 在初始化函数内部，每个工作进程根据接收到的句柄，将共享内存块重新“视图”为NumPy数组。这样，每个工作进程都可以在不复制数据的情况下，直接访问大型静态数据。  
5. **高效的任务分发:** 完成初始化后，主进程的pool.map调用现在只需要传递小体积的动态数据——即当前时间步的雨量值数组。

通过这种方式，我们极大地减少了进程间通信的开销，显著降低了内存占用，从根本上解决了大规模并行地理空间计算中的性能瓶颈。

#### **9.4 Dask：一个更高层次的替代方案**

值得一提的是，Dask库为Python中的并行和核外（out-of-core）计算提供了另一个强大且更高级的抽象 46。

Dask能够将大型NumPy数组或Pandas DataFrame在逻辑上切分成许多小块（chunks），并以“懒加载”（lazy evaluation）的方式构建计算图。当调用.compute()时，Dask的调度器会自动将计算任务并行化。

特别是，rioxarray库（一个基于rasterio和xarray的库）与Dask有很好的集成，可以非常方便地以分块方式打开和处理大型栅格文件，实现核外处理 48。对于未来需要处理远超内存容量的TB级栅格数据的应用，

Dask和rioxarray将是理想的技术选择。

### **第10节：确保鲁棒性：错误与异常处理**

一个生产级的科学计算软件不仅要算得对、算得快，还必须足够健壮，能够优雅地处理各种预料之外的错误。本系统将内置一套全面的错误和异常处理机制。

#### **10.1 常见的地理空间数据错误**

在处理地理空间数据时，一些常见问题可能导致程序失败。系统将在数据加载阶段进行预检，以捕获这些问题：

* **坐标参考系统 (CRS) 不匹配:** 如果输入的站点数据和地形栅格数据使用了不同的CRS，空间关系计算将是错误的。代码会检查并确保所有输入的空间数据都在同一个CRS下，否则将发出警告或停止 51。  
* **几何无效:** 例如，自相交的多边形或无效的几何对象。虽然本项目主要处理点和栅格，但在涉及矢量掩膜时，这可能成为一个问题。  
* **数据缺失或格式错误:** 文件不存在、CSV列名错误、数据类型不匹配等。这些将通过标准的try...except块进行捕获，并向用户提供清晰的错误信息。

#### **10.2 处理克里金和OI中的奇异矩阵错误**

**问题根源:** 在克里金和OI的计算中，都需要求解一个线性方程组，其核心是协方差矩阵的求逆。如果两个或多个雨量站的位置完全相同或极其接近，会导致协方差矩阵中的行或列线性相关，使得该矩阵成为“奇异矩阵”（或病态矩阵），其行列式为零或接近于零，从而无法求逆，导致计算失败 53。这是一个在实践中非常常见的问题。

**解决方案:** 系统将采用多层次策略来应对此问题：

1. **数据预处理:** 在加载stations.csv时，代码将检查是否存在坐标完全相同的站点。如果存在，将向用户发出警告，并建议在数据源头进行清理。  
2. **利用块金效应 (Nugget Effect):** 在克里金法中，变异函数的块金值（nugget）在数学上起到了对协方差矩阵对角线元素进行正则化的作用。即使两个点距离为零，它们的协方差也不是完美的1，而是sill \- nugget。这确保了对角线元素始终大于非对角线元素，从而保证了矩阵的正定性和可逆性。在config.yml中，将确保nugget的默认值或优化下限为一个小的正数，这是避免奇异矩阵最自然和最符合地统计学理论的方法。  
3. **使用伪逆 (Pseudo-Inverse):** 在极端情况下，如果矩阵仍然接近奇异，PyKrige库提供了一个备用方案。通过在OrdinaryKriging初始化时设置pseudo\_inv=True，库将使用奇异值分解（SVD）等方法计算伪逆，而不是标准的矩阵逆 32。这提供了一个强大的后备机制来保证计算的继续进行。此选项将在  
   config.yml中作为可配置项提供。

#### **10.3 rasterio与I/O异常**

所有文件的读写操作都将被置于try...except块中。这将捕获rasterio.errors模块中定义的特定异常，如RasterioIOError（文件无法打开或访问）、DriverCapabilityError（格式驱动不支持写入）等 55。当捕获到这些异常时，程序将记录详细的错误信息并优雅地终止，而不是意外崩溃，从而为用户提供清晰的调试线索。

## **第四部分：验证与用户指南**

### **第11节：定量模型评估**

对插值结果进行客观、定量的评估是验证模型性能和比较不同方法优劣的唯一科学途径。本系统集成了一套标准的评估流程。

#### **11.1 留一法交叉验证 (LOOCV)**

本系统将采用留一法交叉验证（Leave-One-Out Cross-Validation, LOOCV）策略，专门针对用户在Delaunay\_molan.csv中指定的target\_station进行评估。其流程如下：

1. 对于一个给定的洪水事件，系统首先从Delaunay\_molan.csv中获取需要被插值的target\_station列表。  
2. 系统将遍历这个列表中的每一个target\_station。  
3. 在每一次迭代中，当前遍历到的target\_station将被视为“未知点”，其真实的观测值将被暂时“隐藏”。  
4. 系统将使用该target\_station在Delaunay\_molan.csv中对应的interp\_stations\_info列表中的其他站点作为输入数据。  
5. 调用选定的插值方法（如Kriging），预测该target\_station位置的降雨量值。  
6. 将预测值与该站点的真实观测值进行比较，计算误差。  
7. 这个过程对所有target\_station重复进行，最终得到每个目标站点在每个时间步的预测值和真实值对，为计算整体评估指标提供了基础。

#### **11.2 评估指标**

系统将计算并报告四种在水文学和气象学中广泛使用的评估指标，以全面评价模型性能：

* 均方根误差 (Root Mean Square Error, RMSE):  
  RMSE=N1​i=1∑N​(Pi​−Oi​)2​

  RMSE对较大的误差给予更高的权重，是衡量模型平均预测误差的常用指标。值越小越好。  
* 平均绝对误差 (Mean Absolute Error, MAE):  
  MAE=N1​i=1∑N​∣Pi​−Oi​∣

  MAE直接衡量预测误差的平均大小，对异常值不如RMSE敏感。值越小越好。  
* 决定系数 (R2):  
  $$ R^2 \= \\left( \\frac{\\sum\_{i=1}^{N} (O\_i \- \\bar{O})(P\_i \- \\bar{P})}{\\sqrt{\\sum\_{i=1}^{N} (O\_i \- \\bar{O})^2} \\sqrt{\\sum\_{i=1}^{N} (P\_i \- \\bar{P})^2}} \\right)^2 $$  
  R2 衡量预测值与观测值之间线性关系的密切程度，值域为0到1。值越接近1，表示模型对数据变异性的解释能力越强 57。  
* 纳什效率系数 (Nash-Sutcliffe Efficiency, NSE):  
  NSE=1−∑i=1N​(Oi​−Oˉ)2∑i=1N​(Pi​−Oi​)2​

  NSE是水文模型评估中最常用的指标之一。它将模型的预测结果与使用观测值平均值作为预测进行比较。NSE的值域为负无穷到1。NSE \= 1表示完美匹配；NSE \= 0表示模型预测与平均值一样好；NSE \< 0表示模型预测还不如直接使用观测平均值 58。

其中，Pi​ 是第 i 个预测值，Oi​ 是第 i 个观测值，N 是总样本数，Pˉ 和 Oˉ 分别是预测值和观测值的平均值。

#### **11.3 关于空间自相关在验证中的说明**

提供一个更深层次的专业视角是本报告的目标之一。虽然LOOCV是一种标准且有效的交叉验证技术，但在处理地理空间数据时，研究者必须意识到其潜在的局限性。由于**空间自相关**的存在（即邻近位置的属性值相似），通过LOOCV选出的训练集和验证点在空间上可能非常接近。这会导致模型在验证集上表现得过于乐观，因为它实际上已经在训练集中“偷看”到了非常相似的数据 60。

为了更严格地评估模型对全新、未见区域的泛化能力，学术界发展了**空间交叉验证 (Spatial Cross-Validation)**。这类方法通过空间上的分割来确保训练集和验证集之间的独立性，例如：

* **块状交叉验证 (Block CV):** 将研究区划分为地理网格，轮流将整个网格块作为验证集。  
* **缓冲区交叉验证 (Buffer CV):** 在每个验证点周围创建一个缓冲区，将缓冲区内的所有其他点从训练集中移除。

Python中已有专门的库如spatial-kfold 63 和

spacv 64 来实现这些高级验证策略。虽然本项目的当前范围是实现LOOCV，但了解并提及这些更先进的方法，对于期望发表高质量研究成果的用户来说是至关重要的。

### **第12节：用户手册：一份实用操作指南**

本节旨在为用户提供清晰、详尽的操作说明，确保用户能够顺利地安装、配置和运行本软件系统。

#### **12.1 系统需求与安装**

1. **环境设置:** 推荐使用conda来创建一个独立的Python环境，以避免与其他项目的依赖冲突。  
   Bash  
   conda create \-n rainfall\_interp python=3.9  
   conda activate rainfall\_interp

2. **依赖安装:** 项目根目录下将提供一个requirements.txt文件，列出了所有必需的Python库。使用pip进行安装。  
   Bash  
   pip install \-r requirements.txt

   **注意:** 安装地理空间库（如rasterio, geopandas）时，直接使用pip在Windows上可能会遇到困难。强烈建议在conda环境中使用conda-forge频道进行安装，因为它可以更好地处理复杂的二进制依赖关系 65。  
   Bash  
   conda install \-c conda-forge pandas numpy rasterio pykrige sceua pyyaml matplotlib

#### **12.2 目录结构设置**

请确保您的数据严格按照以下结构组织在config.yml中指定的base\_dir下。如果output目录不存在，程序将在首次运行时自动创建。

D:/pythondata/jiangyuchazhi/  
├── input\_another/  
│   └── 2009-1/  
│       ├── 3633.csv  
│       ├── 3634.csv  
│       └──...  
├── terrain/  
│   └── 90/  
│       ├── DEM.asc  
│       ├── aspect.asc  
│       ├── mask.asc  
│       └── slope.asc  
├── output/  
├── stations.csv  
└── Delaunay\_molan.csv

#### **12.3 运行插值**

所有操作都通过项目根目录下的run.py脚本执行。打开命令行终端，激活conda环境，然后根据您的需求执行以下命令。

* **场景一：对单一洪水事件'2009-1'运行PRISM方法**  
  Bash  
  python run.py \--method PRISM \--flood 2009-1

* **场景二：对所有洪水事件运行PRISM方法**  
  Bash  
  python run.py \--method PRISM \--flood all

* **场景三：对单一洪水事件'2009-1'运行所有方法**  
  Bash  
  python run.py \--method all \--flood 2009-1

* **场景四：对所有洪水事件运行所有方法（全量批处理）**  
  Bash  
  python run.py \--method all \--flood all

  在此模式下，程序将遍历input\_another下的所有子文件夹，对每个文件夹执行config.yml中methods\_to\_run指定的所有方法，并最终生成一个包含所有结果的评估摘要文件。

#### **12.4 理解输出**

* **栅格文件:** 如果generate\_raster\_output设置为true，程序将在output/{method}/{flood\_event}/目录下生成一系列.asc格式的栅格文件。文件名将包含时间戳信息，例如rainfall\_2009-01-15T14-00-00.asc，方便后续的可视化和分析。文件名中的特殊字符（如冒号）已被替换，以确保跨操作系统的兼容性。  
* **评估摘要文件:** 当以批处理模式（例如，--flood all）运行时，程序将在output/目录下生成一个名为evaluation\_summary.csv的文件。该文件汇总了所有方法在所有洪水事件中对所有目标站点的评估指标，是进行最终性能比较的核心依据。

#### **关键表3: 批处理输出 (evaluation\_summary.csv) 结构**

| 列名 | 数据类型 | 描述 |
| :---- | :---- | :---- |
| FloodEvent | string | 洪水事件的标识符（如 '2009-1'）。 |
| Method | string | 使用的插值方法（如 'Kriging', 'IDW'）。 |
| RMSE | float | 该方法在该事件下的整体均方根误差。 |
| MAE | float | 该方法在该事件下的整体平均绝对误差。 |
| R2 | float | 该方法在该事件下的整体决定系数。 |
| NSE | float | 该方法在该事件下的整体纳什效率系数。 |

### **结论与展望**

本报告详细阐述了一个全面、高性能、可配置的降雨空间插值系统的设计与实现。该系统集成了四种核心插值方法（IDW, Kriging, OI, PRISM），并引入了先进的自动化参数优化（SCE-UA）和高性能计算技术（多进程并行与共享内存）。其最显著的特点是采纳了用户提供的先验知识（Delaunay\_molan.csv），实现了动态、事件特定的插值站点选择，这使得插值过程更具针对性和科学性。

通过对数据管道、核心算法、性能优化和错误处理的深入探讨，本系统不仅能够满足用户当前的研究需求，更为其提供了一个透明、可扩展的科学计算平台。用户可以通过中心化的config.yml文件和灵活的命令行接口，轻松地进行各种对比实验，并获得定量的评估结果，从而为特定流域选择最适宜的降雨插值方案。

**未来展望:**

1. **更复杂的模型:** 当前框架可以轻松扩展，以集成更先进的插值技术，如通用克里金（Universal Kriging，考虑趋势项）、协同克里金（Co-Kriging，引入高程等协变量）或基于机器学习的插值方法。  
2. **处理零降雨的进阶方法:** 可以实现指示克里金（Indicator Kriging）或基于零膨胀分布的模型，以更严谨地处理降雨数据中的大量零值。  
3. **模型等效性（Equifinality）分析:** 水文模型普遍存在“等效性”问题，即多组不同的参数可以产生相似的模拟结果 66。未来的工作可以利用GLUE（Generalized Likelihood Uncertainty Estimation）等方法，探索不同插值方法和参数组合下的预测不确定性，而不仅仅是寻找单一的最优解。  
4. **集成Dask:** 为了处理更大规模的数据（如全国范围、更高时空分辨率），可以将当前的multiprocessing后端替换为Dask，以利用其强大的核外计算和分布式计算能力。

总之，本报告所描述的系统是一个起点，它不仅解决了用户提出的具体问题，也为用户在降雨空间插值领域的深入研究和探索奠定了坚实的技术基础。

#### **引用的著作**

1. RFC Use of PRISM Data \- California Nevada River Forecast Center, 访问时间为 六月 26, 2025， [https://www.cnrfc.noaa.gov/products/rfcprismuse.pdf](https://www.cnrfc.noaa.gov/products/rfcprismuse.pdf)  
2. A statistical-topographic model for mapping climatological ..., 访问时间为 六月 26, 2025， [https://prism.nacse.org/documents/pubs/1994jappclim\_mountainPrecip\_gibson.pdf](https://prism.nacse.org/documents/pubs/1994jappclim_mountainPrecip_gibson.pdf)  
3. Basic Steps in Geostatistics: The Variogram and Kriging, 访问时间为 六月 26, 2025， [https://www.geokniga.org/bookfiles/geokniga-basicstepsingeostatistics.pdf](https://www.geokniga.org/bookfiles/geokniga-basicstepsingeostatistics.pdf)  
4. Optimal Interpolation \- Earth, Atmospheric, and Planetary Physics, 访问时间为 六月 26, 2025， [https://www.atmosp.physics.utoronto.ca/PHY2509/ch3.pdf](https://www.atmosp.physics.utoronto.ca/PHY2509/ch3.pdf)  
5. Optimal Interpolation Mastery \- Number Analytics, 访问时间为 六月 26, 2025， [https://www.numberanalytics.com/blog/optimal-interpolation-weather-forecasting-guide](https://www.numberanalytics.com/blog/optimal-interpolation-weather-forecasting-guide)  
6. The Prism Approach to Mapping Precipitation and Temperature, 访问时间为 六月 26, 2025， [https://prism.nacse.org/documents/pubs/1997appclim\_PRISMapproach\_daly.pdf](https://prism.nacse.org/documents/pubs/1997appclim_PRISMapproach_daly.pdf)  
7. What's the best practice using a settings(config) file in Python? \[closed\] \- Stack Overflow, 访问时间为 六月 26, 2025， [https://stackoverflow.com/questions/5055042/whats-the-best-practice-using-a-settingsconfig-file-in-python](https://stackoverflow.com/questions/5055042/whats-the-best-practice-using-a-settingsconfig-file-in-python)  
8. Working with Python Configuration Files: Tutorial & Best Practices \- Configu, 访问时间为 六月 26, 2025， [https://configu.com/blog/working-with-python-configuration-files-tutorial-best-practices/](https://configu.com/blog/working-with-python-configuration-files-tutorial-best-practices/)  
9. How to Write Configuration Files in Your Machine Learning Project. \- Medium, 访问时间为 六月 26, 2025， [https://medium.com/analytics-vidhya/how-to-write-configuration-files-in-your-machine-learning-project-47bc840acc19](https://medium.com/analytics-vidhya/how-to-write-configuration-files-in-your-machine-learning-project-47bc840acc19)  
10. Navigating the CLI Landscape in Python: A Comparative Study of argparse, click, and typer, 访问时间为 六月 26, 2025， [https://medium.com/@mohd\_nass/navigating-the-cli-landscape-in-python-a-comparative-study-of-argparse-click-and-typer-480ebbb7172f](https://medium.com/@mohd_nass/navigating-the-cli-landscape-in-python-a-comparative-study-of-argparse-click-and-typer-480ebbb7172f)  
11. Click vs argparse \- Which CLI Package is Better? \- Python Snacks, 访问时间为 六月 26, 2025， [https://www.pythonsnacks.com/p/click-vs-argparse-python](https://www.pythonsnacks.com/p/click-vs-argparse-python)  
12. Build Command-Line Interfaces With Python's argparse \- Real Python, 访问时间为 六月 26, 2025， [https://realpython.com/command-line-interfaces-python-argparse/](https://realpython.com/command-line-interfaces-python-argparse/)  
13. Python \- Difference between docopt and argparse \- Stack Overflow, 访问时间为 六月 26, 2025， [https://stackoverflow.com/questions/20599513/python-difference-between-docopt-and-argparse](https://stackoverflow.com/questions/20599513/python-difference-between-docopt-and-argparse)  
14. Reading raster files with Rasterio — Intro to Python GIS documentation, 访问时间为 六月 26, 2025， [https://automating-gis-processes.github.io/CSC18/lessons/L6/reading-raster.html](https://automating-gis-processes.github.io/CSC18/lessons/L6/reading-raster.html)  
15. Raster processing using Python Tools: Working with Raster Datasets, 访问时间为 六月 26, 2025， [https://geohackweek.github.io/raster/04-workingwithrasters/](https://geohackweek.github.io/raster/04-workingwithrasters/)  
16. Rasters (rasterio) — Spatial Data Programming with Python \- Michael Dorman, 访问时间为 六月 26, 2025， [https://geobgu.xyz/py-2024/10-rasterio1.html](https://geobgu.xyz/py-2024/10-rasterio1.html)  
17. Session 6: Raster Data Analysis \- our documentation, 访问时间为 六月 26, 2025， [https://geospatialyst.readthedocs.io/en/latest/Content/Lesson/geo-python-course/06.Raster-data-analysis.html](https://geospatialyst.readthedocs.io/en/latest/Content/Lesson/geo-python-course/06.Raster-data-analysis.html)  
18. Space–Time Kriging of Precipitation: Modeling the Large-Scale ..., 访问时间为 六月 26, 2025， [https://www.mdpi.com/2073-4441/11/11/2368](https://www.mdpi.com/2073-4441/11/11/2368)  
19. Mastering Inverse Distance Weighting \- Number Analytics, 访问时间为 六月 26, 2025， [https://www.numberanalytics.com/blog/mastering-inverse-distance-weighting](https://www.numberanalytics.com/blog/mastering-inverse-distance-weighting)  
20. Spatial Interpolation Methods, 访问时间为 六月 26, 2025， [https://iri.columbia.edu/\~rijaf/CDTUserGuide/html/interpolation\_methods.html](https://iri.columbia.edu/~rijaf/CDTUserGuide/html/interpolation_methods.html)  
21. How inverse distance weighted interpolation works—ArcGIS Pro | Documentation, 访问时间为 六月 26, 2025， [https://pro.arcgis.com/en/pro-app/latest/help/analysis/geostatistical-analyst/how-inverse-distance-weighted-interpolation-works.htm](https://pro.arcgis.com/en/pro-app/latest/help/analysis/geostatistical-analyst/how-inverse-distance-weighted-interpolation-works.htm)  
22. Computing the Beta Parameter in IDW Interpolation by Using a Genetic Algorithm \- MDPI, 访问时间为 六月 26, 2025， [https://www.mdpi.com/2073-4441/13/6/863](https://www.mdpi.com/2073-4441/13/6/863)  
23. Spatial interpolation: which technique is best & how to run it \- CARTO, 访问时间为 六月 26, 2025， [https://carto.com/blog/spatial-interpolation-techniques-tutorial](https://carto.com/blog/spatial-interpolation-techniques-tutorial)  
24. IDW (Spatial Analyst)—ArcGIS Pro | Documentation, 访问时间为 六月 26, 2025， [https://pro.arcgis.com/en/pro-app/latest/tool-reference/spatial-analyst/idw.htm](https://pro.arcgis.com/en/pro-app/latest/tool-reference/spatial-analyst/idw.htm)  
25. Unlocking Spatial Insights with Universal Kriging \- Number Analytics, 访问时间为 六月 26, 2025， [https://www.numberanalytics.com/blog/universal-kriging-geostatistical-analysis-prediction](https://www.numberanalytics.com/blog/universal-kriging-geostatistical-analysis-prediction)  
26. Estimation of Precipitation by Kriging in the EOF Space of theSea Level Pressure Field in, 访问时间为 六月 26, 2025， [https://journals.ametsoc.org/view/journals/clim/12/4/1520-0442\_1999\_012\_1070\_eopbki\_2.0.co\_2.xml](https://journals.ametsoc.org/view/journals/clim/12/4/1520-0442_1999_012_1070_eopbki_2.0.co_2.xml)  
27. Interpolation, Kriging, Gaussian Processes \- Duke People, 访问时间为 六月 26, 2025， [https://people.duke.edu/\~hpgavin/Risk/interpolation.pdf](https://people.duke.edu/~hpgavin/Risk/interpolation.pdf)  
28. Ordinary Kriging Example — PyKrige 1.7.2 documentation \- GeoStat ReadTheDocs, 访问时间为 六月 26, 2025， [https://geostat-framework.readthedocs.io/projects/pykrige/en/stable/examples/00\_ordinary.html](https://geostat-framework.readthedocs.io/projects/pykrige/en/stable/examples/00_ordinary.html)  
29. PyKrige 1.4.0 documentation \- GeoStat ReadTheDocs, 访问时间为 六月 26, 2025， [https://geostat-framework.readthedocs.io/projects/pykrige/en/v1.4.0/overview.html](https://geostat-framework.readthedocs.io/projects/pykrige/en/v1.4.0/overview.html)  
30. PyKrige 1.5.0 documentation \- GeoStat ReadTheDocs, 访问时间为 六月 26, 2025， [https://geostat-framework.readthedocs.io/projects/pykrige/en/v1.5.0/](https://geostat-framework.readthedocs.io/projects/pykrige/en/v1.5.0/)  
31. pykrige.uk.UniversalKriging \- GeoStat ReadTheDocs, 访问时间为 六月 26, 2025， [https://geostat-framework.readthedocs.io/projects/pykrige/en/stable/generated/pykrige.uk.UniversalKriging.html](https://geostat-framework.readthedocs.io/projects/pykrige/en/stable/generated/pykrige.uk.UniversalKriging.html)  
32. pykrige.ok.OrdinaryKriging \- GeoStat ReadTheDocs, 访问时间为 六月 26, 2025， [https://geostat-framework.readthedocs.io/projects/pykrige/en/stable/generated/pykrige.ok.OrdinaryKriging.html](https://geostat-framework.readthedocs.io/projects/pykrige/en/stable/generated/pykrige.ok.OrdinaryKriging.html)  
33. Interface with GSTools · Issue \#124 · GeoStat-Framework/PyKrige \- GitHub, 访问时间为 六月 26, 2025， [https://github.com/bsmurphy/PyKrige/issues/124](https://github.com/bsmurphy/PyKrige/issues/124)  
34. Optimal Interpolation (§5.4), 访问时间为 六月 26, 2025， [https://maths.ucd.ie/\~plynch/LECTURE-NOTES/NWP-2004/NWP-CH05-4-1.pdf](https://maths.ucd.ie/~plynch/LECTURE-NOTES/NWP-2004/NWP-CH05-4-1.pdf)  
35. PRISM PRECIPITATION DATA \- University of Idaho, 访问时间为 六月 26, 2025， [https://objects.lib.uidaho.edu/iwdl/iwdl-ddw011\_prism\_031303.pdf](https://objects.lib.uidaho.edu/iwdl/iwdl-ddw011_prism_031303.pdf)  
36. Optimal Interpolation (§5.4), 访问时间为 六月 26, 2025， [https://maths.ucd.ie/\~plynch/LECTURE-NOTES/NWP-2004/NWP-CH05-4-1-P4.pdf](https://maths.ucd.ie/~plynch/LECTURE-NOTES/NWP-2004/NWP-CH05-4-1-P4.pdf)  
37. Physiographically sensitive mapping of climatological temperature ..., 访问时间为 六月 26, 2025， [https://prism.nacse.org/documents/pubs/2008intjclim\_physiographicMapping\_daly.pdf](https://prism.nacse.org/documents/pubs/2008intjclim_physiographicMapping_daly.pdf)  
38. Shuffled Complex Evolution Model Calibrating Algorithm: Enhancing its Robustness and Efficiency \- VU Research Repository, 访问时间为 六月 26, 2025， [https://vuir.vu.edu.au/3856/1/08%20-%20Shuffled%20Complex%20Evolution%20Model%20Calibrating%20Algor%20.pdf](https://vuir.vu.edu.au/3856/1/08%20-%20Shuffled%20Complex%20Evolution%20Model%20Calibrating%20Algor%20.pdf)  
39. (PDF) A Shuffled Complex Evolution Metropolis algorithm optimization and uncertainty assessment of hydrologic models parameters \- ResearchGate, 访问时间为 六月 26, 2025， [https://www.researchgate.net/publication/228696681\_A\_Shuffled\_Complex\_Evolution\_Metropolis\_algorithm\_optimization\_and\_uncertainty\_assessment\_of\_hydrologic\_models\_parameters](https://www.researchgate.net/publication/228696681_A_Shuffled_Complex_Evolution_Metropolis_algorithm_optimization_and_uncertainty_assessment_of_hydrologic_models_parameters)  
40. Shuffled Complex Evolution (SCE-UA) Method \- File Exchange \- MATLAB Central, 访问时间为 六月 26, 2025， [https://www.mathworks.com/matlabcentral/fileexchange/7671-shuffled-complex-evolution-sce-ua-method](https://www.mathworks.com/matlabcentral/fileexchange/7671-shuffled-complex-evolution-sce-ua-method)  
41. Optimal Use of the SCE-UA Global Optimization Method for Calibrating Watershed Models, 访问时间为 六月 26, 2025， [https://www.researchgate.net/publication/223408756\_Optimal\_Use\_of\_the\_SCE-UA\_Global\_Optimization\_Method\_for\_Calibrating\_Watershed\_Models](https://www.researchgate.net/publication/223408756_Optimal_Use_of_the_SCE-UA_Global_Optimization_Method_for_Calibrating_Watershed_Models)  
42. A novel fast and efficient adaptive shuffled complex evolution algorithm for model parameter calibration \- Frontiers, 访问时间为 六月 26, 2025， [https://www.frontiersin.org/journals/environmental-science/articles/10.3389/fenvs.2023.1341017/full](https://www.frontiersin.org/journals/environmental-science/articles/10.3389/fenvs.2023.1341017/full)  
43. cheginit/sceua: SCE-UA: Shuffle Complex Evolution ... \- GitHub, 访问时间为 六月 26, 2025， [https://github.com/cheginit/sceua](https://github.com/cheginit/sceua)  
44. On Sharing Large Arrays When Using Python's Multiprocessing \- Mianzhi Wang, 访问时间为 六月 26, 2025， [https://research.wmz.ninja/articles/2018/03/on-sharing-large-arrays-when-using-pythons-multiprocessing.html](https://research.wmz.ninja/articles/2018/03/on-sharing-large-arrays-when-using-pythons-multiprocessing.html)  
45. Sharing a large numpy array across python multiprocessing map \- Stack Overflow, 访问时间为 六月 26, 2025， [https://stackoverflow.com/questions/76301413/sharing-a-large-numpy-array-across-python-multiprocessing-map](https://stackoverflow.com/questions/76301413/sharing-a-large-numpy-array-across-python-multiprocessing-map)  
46. Parallel Computing with Dash and Dask | Dash for Python Documentation | Plotly, 访问时间为 六月 26, 2025， [https://dash.plotly.com/dask-dash](https://dash.plotly.com/dask-dash)  
47. Scalable and Computationally Reproducible Approaches to Arctic Research \- 9 Parallelization with Dask, 访问时间为 六月 26, 2025， [https://learning.nceas.ucsb.edu/2022-09-arctic/sections/09-parallel-with-dask.html](https://learning.nceas.ucsb.edu/2022-09-arctic/sections/09-parallel-with-dask.html)  
48. Parallel raster computations using Dask \- The Carpentries Incubator, 访问时间为 六月 26, 2025， [https://carpentries-incubator.github.io/geospatial-python/instructor/11-parallel-raster-computations.html](https://carpentries-incubator.github.io/geospatial-python/instructor/11-parallel-raster-computations.html)  
49. Parallel Computing with Dask \- Xarray documentation, 访问时间为 六月 26, 2025， [https://docs.xarray.dev/en/stable/user-guide/dask.html](https://docs.xarray.dev/en/stable/user-guide/dask.html)  
50. Scalable and Computationally Reproducible Approaches to Arctic Research \- 6 Parallelization with Dask, 访问时间为 六月 26, 2025， [https://learning.nceas.ucsb.edu/2023-03-arctic/sections/parallel-with-dask.html](https://learning.nceas.ucsb.edu/2023-03-arctic/sections/parallel-with-dask.html)  
51. Analyze Geospatial Data in Python: GeoPandas and Shapely \- LearnDataSci, 访问时间为 六月 26, 2025， [https://www.learndatasci.com/tutorials/geospatial-data-python-geopandas-shapely/](https://www.learndatasci.com/tutorials/geospatial-data-python-geopandas-shapely/)  
52. ValueError: Input shapes do not overlap raster. Geopandas/Rasterio, possible CRS error when masking \- Stack Overflow, 访问时间为 六月 26, 2025， [https://stackoverflow.com/questions/70022058/valueerror-input-shapes-do-not-overlap-raster-geopandas-rasterio-possible-crs](https://stackoverflow.com/questions/70022058/valueerror-input-shapes-do-not-overlap-raster-geopandas-rasterio-possible-crs)  
53. How to avoid the problem of singular matrices in the ordinary Kriging method for spatial interpolation? | ResearchGate, 访问时间为 六月 26, 2025， [https://www.researchgate.net/post/How\_to\_avoid\_the\_problem\_of\_singular\_matrices\_in\_the\_ordinary\_Kriging\_method\_for\_spatial\_interpolation](https://www.researchgate.net/post/How_to_avoid_the_problem_of_singular_matrices_in_the_ordinary_Kriging_method_for_spatial_interpolation)  
54. Board • View topic \- \[SOLVED\] What causes this singular matrix error when kriging \- Geostatistical R Package, 访问时间为 六月 26, 2025， [http://rgeostats.free.fr/forum/viewtopic.php?f=21\&t=499](http://rgeostats.free.fr/forum/viewtopic.php?f=21&t=499)  
55. rasterio.errors module \- Read the Docs, 访问时间为 六月 26, 2025， [https://rasterio.readthedocs.io/en/latest/api/rasterio.errors.html](https://rasterio.readthedocs.io/en/latest/api/rasterio.errors.html)  
56. Error Handling — rasterio 1.5.0.dev documentation \- Read the Docs, 访问时间为 六月 26, 2025， [https://rasterio.readthedocs.io/en/latest/topics/errors.html](https://rasterio.readthedocs.io/en/latest/topics/errors.html)  
57. Pros and cons of various efficiency criteria for hydrological model performance evaluation, 访问时间为 六月 26, 2025， [https://piahs.copernicus.org/articles/385/181/2024/](https://piahs.copernicus.org/articles/385/181/2024/)  
58. Sensitivity analysis of hydrological model parameters based on improved Morris method with the double-Latin hypercube sampling \- IWA Publishing, 访问时间为 六月 26, 2025， [https://iwaponline.com/hr/article/54/2/220/93102/Sensitivity-analysis-of-hydrological-model](https://iwaponline.com/hr/article/54/2/220/93102/Sensitivity-analysis-of-hydrological-model)  
59. On the choice of calibration metrics for “high-flow” estimation using hydrologic models, 访问时间为 六月 26, 2025， [https://hess.copernicus.org/articles/23/2601/2019/](https://hess.copernicus.org/articles/23/2601/2019/)  
60. Spatial cross-validation for GeoAI 1, 访问时间为 六月 26, 2025， [https://www.acsu.buffalo.edu/\~yhu42/papers/2023\_GeoAIHandbook\_SpatialCV.pdf](https://www.acsu.buffalo.edu/~yhu42/papers/2023_GeoAIHandbook_SpatialCV.pdf)  
61. Spatial Cross-Validation for GeoAI | Request PDF \- ResearchGate, 访问时间为 六月 26, 2025， [https://www.researchgate.net/publication/375237058\_Spatial\_cross-validation\_for\_GeoAI](https://www.researchgate.net/publication/375237058_Spatial_cross-validation_for_GeoAI)  
62. Spatial cross-validation using scikit-learn | Towards Data Science, 访问时间为 六月 26, 2025， [https://towardsdatascience.com/spatial-cross-validation-using-scikit-learn-74cb8ffe0ab9/](https://towardsdatascience.com/spatial-cross-validation-using-scikit-learn-74cb8ffe0ab9/)  
63. spatial-kfold \- PyPI, 访问时间为 六月 26, 2025， [https://pypi.org/project/spatial-kfold/](https://pypi.org/project/spatial-kfold/)  
64. SamComber/spacv: Spatial cross-validation in Python. \- GitHub, 访问时间为 六月 26, 2025， [https://github.com/SamComber/spacv](https://github.com/SamComber/spacv)  
65. Error installing geopandas:" A GDAL API version must be specified " in Anaconda, 访问时间为 六月 26, 2025， [https://stackoverflow.com/questions/54734667/error-installing-geopandas-a-gdal-api-version-must-be-specified-in-anaconda](https://stackoverflow.com/questions/54734667/error-installing-geopandas-a-gdal-api-version-must-be-specified-in-anaconda)  
66. Equifinality \- Wikipedia, 访问时间为 六月 26, 2025， [https://en.wikipedia.org/wiki/Equifinality](https://en.wikipedia.org/wiki/Equifinality)  
67. Parallel Processing for a Better Understanding of Equifinality in Hydrological Models \- BYU ScholarsArchive, 访问时间为 六月 26, 2025， [https://scholarsarchive.byu.edu/cgi/viewcontent.cgi?article=3645\&context=iemssconference](https://scholarsarchive.byu.edu/cgi/viewcontent.cgi?article=3645&context=iemssconference)  
68. A manifesto for the equifinality thesis \- CiteSeerX, 访问时间为 六月 26, 2025， [https://citeseerx.ist.psu.edu/document?repid=rep1\&type=pdf\&doi=d882f7133e542ddc398a8b4e71428fc63423d1e1](https://citeseerx.ist.psu.edu/document?repid=rep1&type=pdf&doi=d882f7133e542ddc398a8b4e71428fc63423d1e1)