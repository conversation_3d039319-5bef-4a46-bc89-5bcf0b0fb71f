# ---------------------------------------------------------------------------
#                      降雨空间插值系统 - 配置文件
# ---------------------------------------------------------------------------

# 1. 项目路径设置
project_paths:
  base_dir: "D:/pythondata/jiangyuchazhi/"
  # 输入洪水事件文件夹模板, {flood_event} 将被替换为 '2009-1' 等
  input_dir_template: "input_another/{flood_event}"
  # 输出文件夹模板, {method} 和 {flood_event} 将被替换
  output_dir_template: "output/{method}/{flood_event}"
  terrain_dir: "terrain/90/"
  station_file: "stations.csv"
  delaunay_molan_file: "Delaunay_Molan.csv"

# 2. 运行设置
run_settings:
  # 要运行的方法列表, 可选项: 'IDW', 'Kriging', 'OI', 'PRISM', 或 'all'
  methods_to_run: ['all']
  # 要运行的洪水场次列表, 或 'all' 表示遍历所有
  floods_to_run: ['all']
  # 使用的CPU核心数
  cpu_cores: 8
  # 是否生成每个时刻的流域面雨量栅格输出
  generate_raster_output: true
  # 栅格和参数优化的时间限制（秒）
  max_raster_time: 300
  max_optimization_time: 1800

# 3. 栅格设置 (将从mask.asc自动读取，此处为备用)
grid_settings:
  # 这些值将从mask.asc文件自动推断, 无需手动设置
  # nrows: 
  # ncols: 
  # xllcorner: 
  # yllcorner: 
  # cellsize: 
  nodata_value: -9999

# 4. 各插值方法参数
method_params:
  IDW:
    power: 2.0
  Kriging:
    # 可选: 'linear', 'power', 'gaussian', 'spherical', 'exponential'
    variogram_model: 'spherical'
    # 如果不进行优化，将使用这里的参数。格式为 [sill, range, nugget]
    # 如果进行优化，这里的值可以作为优化的初始猜测
    variogram_parameters: [10.0, 100000.0, 1.0]
    # 各向异性参数
    anisotropy_scaling: 1.0
    anisotropy_angle: 0.0
    # 零值处理策略: 'standard', 'indicator', 'transform'
    zero_handling: 'standard'
    # 零值阈值（当零值占比超过此值时启用指示克里金）
    zero_threshold: 0.7
  OI:
    # 观测误差方差 (sigma_r^2)
    observation_error_variance: 0.5
    # 背景场误差相关长度 (米)
    background_error_corr_length: 50000.0
  PRISM:
    # 搜索半径 (米)
    search_radius: 150000.0
    # 最小和最大邻近站点数
    min_stations: 5
    max_stations: 20
    # 距离权重指数
    distance_power: 2.0
    # 高程权重指数
    elevation_power: 1.0

# 5. 参数优化设置
optimization_settings:
  # 是否启用参数优化
  enable_optimization: true
  optimizer: 'SCE-UA'
  # 需要优化的方法列表
  methods_to_optimize: ['Kriging', 'OI', 'PRISM']
  # SCE-UA 算法参数
  sceua_params:
    max_iterations: 1000
    n_complexes: 5
    # 交叉验证的目标函数, 可选: 'rmse', 'nse_mean', 'weighted_rmse'
    objective_function: 'nse_mean'
  # 各方法参数的优化搜索边界
  optimization_bounds:
    Kriging:
      # sill, range, nugget
      variogram_parameters: [[1.0, 50.0], [10000.0, 200000.0], [0.1, 5.0]]
    OI:
      # observation_error_variance, background_error_corr_length
      params: [[0.1, 2.0], [20000.0, 100000.0]]
    PRISM:
      # search_radius, distance_power, elevation_power
      params: [[50000.0, 300000.0], [1.0, 3.0], [0.5, 2.0]]

# 6. 错误处理设置
error_handling:
  # 是否在克里金中使用伪逆
  use_pseudo_inverse: true
  # 最大重试次数
  max_retries: 3
  # 是否详细记录错误
  verbose_errors: true

# 7. 输出设置
output_settings:
  # 文件名时间格式
  time_format: "%Y-%m-%dT%H-%M-%S"
  # 是否保存中间结果
  save_intermediate: false
  # 评估报告格式
  report_format: "csv"
