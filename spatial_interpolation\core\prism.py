"""
降雨空间插值系统 - PRISM插值算法

参数-高程独立坡面回归模型（PRISM）的实现。

作者: Augment Agent
日期: 2025-06-26
"""

import numpy as np
from scipy.spatial.distance import cdist
from scipy.spatial import KDTree
from sklearn.linear_model import LinearRegression
from typing import Tuple, Optional, List, Dict
import warnings

from ..utils.error_handler import InterpolationError, ErrorHandler


class PRISMInterpolator:
    """PRISM插值器"""
    
    def __init__(self, config: dict, error_handler: Optional[ErrorHandler] = None):
        self.config = config
        self.error_handler = error_handler or ErrorHandler()
        
        # 获取配置参数
        prism_config = config['method_params']['PRISM']
        self.search_radius = prism_config['search_radius']
        self.min_stations = prism_config['min_stations']
        self.max_stations = prism_config['max_stations']
        self.distance_power = prism_config['distance_power']
        self.elevation_power = prism_config['elevation_power']
        
        # 地形数据将在插值时传入
        self.dem_data = None
        self.slope_data = None
        self.aspect_data = None
        self.grid_profile = None
    
    def set_terrain_data(self, dem_data: np.ndarray, slope_data: np.ndarray,
                        aspect_data: np.ndarray, grid_profile: dict) -> None:
        """设置地形数据"""
        self.dem_data = dem_data
        self.slope_data = slope_data
        self.aspect_data = aspect_data
        self.grid_profile = grid_profile
    
    def interpolate(self, station_coords: np.ndarray, station_values: np.ndarray,
                   target_coords: np.ndarray, mask: Optional[np.ndarray] = None) -> np.ndarray:
        """
        执行PRISM插值
        
        参数:
            station_coords: 站点坐标 (N, 2)
            station_values: 站点值 (N,)
            target_coords: 目标点坐标 (M, 2)
            mask: 掩膜数组
            
        返回:
            插值结果数组 (M,)
        """
        try:
            # 检查输入数据
            self._validate_inputs(station_coords, station_values, target_coords)
            
            if self.dem_data is None:
                raise InterpolationError("未设置地形数据，无法进行PRISM插值")
            
            # 检查是否所有站点值都为零
            if np.all(station_values == 0):
                self.error_handler.log_info("所有站点值为零，直接返回零值栅格")
                return np.zeros(len(target_coords))
            
            # 过滤异常值
            valid_mask = np.isfinite(station_values) & (station_values >= 0)
            if not valid_mask.all():
                station_coords = station_coords[valid_mask]
                station_values = station_values[valid_mask]
                self.error_handler.log_info(f"过滤异常值，剩余 {len(station_values)} 个有效站点")
            
            if len(station_values) < self.min_stations:
                raise InterpolationError(f"有效站点数量 {len(station_values)} 少于最小要求 {self.min_stations}")
            
            # 获取站点和目标点的高程
            station_elevations = self._get_elevations_at_points(station_coords)
            target_elevations = self._get_elevations_at_points(target_coords)
            
            # 创建KDTree用于快速邻近搜索
            kdtree = KDTree(station_coords)
            
            # 对每个目标点进行插值
            result = np.zeros(len(target_coords))
            
            for i, (target_coord, target_elev) in enumerate(zip(target_coords, target_elevations)):
                if mask is not None and not self._is_valid_pixel(i, mask):
                    result[i] = self.config['grid_settings']['nodata_value']
                    continue
                
                # 执行单点PRISM插值
                result[i] = self._interpolate_single_point(
                    target_coord, target_elev, station_coords, station_values,
                    station_elevations, kdtree
                )
            
            return result
            
        except Exception as e:
            raise InterpolationError(f"PRISM插值失败: {str(e)}")
    
    def _validate_inputs(self, station_coords: np.ndarray, station_values: np.ndarray,
                        target_coords: np.ndarray) -> None:
        """验证输入数据"""
        if len(station_coords) == 0:
            raise InterpolationError("站点坐标为空")
        
        if len(station_values) == 0:
            raise InterpolationError("站点值为空")
        
        if len(station_coords) != len(station_values):
            raise InterpolationError("站点坐标和值的数量不匹配")
        
        if len(target_coords) == 0:
            raise InterpolationError("目标坐标为空")
        
        if station_coords.shape[1] != 2 or target_coords.shape[1] != 2:
            raise InterpolationError("坐标数据必须是2维的")
    
    def _get_elevations_at_points(self, coords: np.ndarray) -> np.ndarray:
        """获取指定坐标点的高程值"""
        try:
            from rasterio.transform import rowcol
            
            transform = self.grid_profile['transform']
            
            # 将地理坐标转换为像元坐标
            rows, cols = rowcol(transform, coords[:, 0], coords[:, 1])
            
            # 确保坐标在有效范围内
            height, width = self.dem_data.shape
            valid_mask = (rows >= 0) & (rows < height) & (cols >= 0) & (cols < width)
            
            elevations = np.full(len(coords), np.nan)
            
            if valid_mask.any():
                valid_rows = rows[valid_mask]
                valid_cols = cols[valid_mask]
                elevations[valid_mask] = self.dem_data[valid_rows, valid_cols]
            
            return elevations
            
        except Exception as e:
            raise InterpolationError(f"获取高程值失败: {str(e)}")
    
    def _interpolate_single_point(self, target_coord: np.ndarray, target_elev: float,
                                 station_coords: np.ndarray, station_values: np.ndarray,
                                 station_elevations: np.ndarray, kdtree: KDTree) -> float:
        """对单个目标点执行PRISM插值"""
        try:
            # 步骤1：在搜索半径内找到邻近站点
            neighbor_indices = kdtree.query_ball_point(target_coord, self.search_radius)
            
            if len(neighbor_indices) < self.min_stations:
                # 如果邻近站点不足，扩大搜索范围或使用最近的站点
                distances, indices = kdtree.query(target_coord, k=self.min_stations)
                neighbor_indices = indices.tolist()
            
            # 限制最大站点数
            if len(neighbor_indices) > self.max_stations:
                distances = cdist([target_coord], station_coords[neighbor_indices])[0]
                sorted_indices = np.argsort(distances)[:self.max_stations]
                neighbor_indices = [neighbor_indices[i] for i in sorted_indices]
            
            # 获取邻近站点的数据
            neighbor_coords = station_coords[neighbor_indices]
            neighbor_values = station_values[neighbor_indices]
            neighbor_elevations = station_elevations[neighbor_indices]
            
            # 过滤有效高程的站点
            valid_elev_mask = np.isfinite(neighbor_elevations) & np.isfinite([target_elev])
            if not valid_elev_mask[:-1].any() or not valid_elev_mask[-1]:
                # 如果没有有效高程数据，使用简单的距离权重
                return self._simple_distance_weighting(
                    target_coord, neighbor_coords, neighbor_values
                )
            
            neighbor_coords = neighbor_coords[valid_elev_mask[:-1]]
            neighbor_values = neighbor_values[valid_elev_mask[:-1]]
            neighbor_elevations = neighbor_elevations[valid_elev_mask[:-1]]
            
            # 步骤2：计算权重
            weights = self._calculate_prism_weights(
                target_coord, target_elev, neighbor_coords, neighbor_elevations
            )
            
            # 步骤3：执行加权线性回归
            if len(neighbor_elevations) >= 2 and np.var(neighbor_elevations) > 0:
                predicted_value = self._weighted_linear_regression(
                    neighbor_elevations, neighbor_values, target_elev, weights
                )
            else:
                # 如果高程变化太小，使用加权平均
                predicted_value = np.average(neighbor_values, weights=weights)
            
            return max(0, predicted_value)  # 确保非负
            
        except Exception as e:
            raise InterpolationError(f"单点PRISM插值失败: {str(e)}")
    
    def _calculate_prism_weights(self, target_coord: np.ndarray, target_elev: float,
                               neighbor_coords: np.ndarray, neighbor_elevations: np.ndarray) -> np.ndarray:
        """计算PRISM权重"""
        try:
            n_neighbors = len(neighbor_coords)
            
            # 计算距离权重 (Wd)
            distances = cdist([target_coord], neighbor_coords)[0]
            distance_weights = 1.0 / (distances ** self.distance_power + 1e-10)  # 避免除零
            
            # 计算高程权重 (Wz)
            elev_differences = np.abs(neighbor_elevations - target_elev)
            elevation_weights = 1.0 / (elev_differences ** self.elevation_power + 1e-10)
            
            # 计算聚类权重 (Wc) - 简化版本
            cluster_weights = self._calculate_cluster_weights(neighbor_coords)
            
            # 计算地形坡面权重 (Wf) - 简化版本
            facet_weights = self._calculate_facet_weights(
                target_coord, neighbor_coords
            )
            
            # 组合所有权重（根据修正后的PRISM公式）
            combined_weights = (distance_weights * elevation_weights * 
                              cluster_weights * facet_weights)
            
            # 归一化权重
            weights_sum = np.sum(combined_weights)
            if weights_sum > 0:
                combined_weights = combined_weights / weights_sum
            else:
                combined_weights = np.ones(n_neighbors) / n_neighbors
            
            return combined_weights
            
        except Exception as e:
            raise InterpolationError(f"计算PRISM权重失败: {str(e)}")
    
    def _calculate_cluster_weights(self, neighbor_coords: np.ndarray) -> np.ndarray:
        """计算聚类权重"""
        try:
            n_neighbors = len(neighbor_coords)
            cluster_weights = np.ones(n_neighbors)
            
            # 计算站点间距离
            distances = cdist(neighbor_coords, neighbor_coords)
            
            # 对于每个站点，计算其周围的站点密度
            for i in range(n_neighbors):
                nearby_count = np.sum(distances[i] < self.search_radius * 0.1)  # 10%的搜索半径
                if nearby_count > 1:
                    cluster_weights[i] = 1.0 / nearby_count
            
            return cluster_weights
            
        except Exception as e:
            self.error_handler.log_warning(f"计算聚类权重失败，使用默认权重: {str(e)}")
            return np.ones(len(neighbor_coords))
    
    def _calculate_facet_weights(self, target_coord: np.ndarray, 
                               neighbor_coords: np.ndarray) -> np.ndarray:
        """计算地形坡面权重"""
        try:
            n_neighbors = len(neighbor_coords)
            facet_weights = np.ones(n_neighbors)
            
            # 获取目标点的坡度和坡向
            target_slope, target_aspect = self._get_slope_aspect_at_point(target_coord)
            
            if np.isnan(target_slope) or np.isnan(target_aspect):
                return facet_weights
            
            # 计算每个邻近站点的坡度和坡向
            for i, neighbor_coord in enumerate(neighbor_coords):
                neighbor_slope, neighbor_aspect = self._get_slope_aspect_at_point(neighbor_coord)
                
                if np.isnan(neighbor_slope) or np.isnan(neighbor_aspect):
                    continue
                
                # 计算坡面相似性
                slope_similarity = 1.0 - abs(target_slope - neighbor_slope) / 90.0
                aspect_similarity = 1.0 - min(abs(target_aspect - neighbor_aspect), 
                                            360 - abs(target_aspect - neighbor_aspect)) / 180.0
                
                facet_weights[i] = (slope_similarity + aspect_similarity) / 2.0
            
            return facet_weights
            
        except Exception as e:
            self.error_handler.log_warning(f"计算坡面权重失败，使用默认权重: {str(e)}")
            return np.ones(len(neighbor_coords))
    
    def _get_slope_aspect_at_point(self, coord: np.ndarray) -> Tuple[float, float]:
        """获取指定点的坡度和坡向"""
        try:
            from rasterio.transform import rowcol
            
            transform = self.grid_profile['transform']
            row, col = rowcol(transform, coord[0], coord[1])
            
            height, width = self.slope_data.shape
            
            if 0 <= row < height and 0 <= col < width:
                slope = self.slope_data[row, col]
                aspect = self.aspect_data[row, col]
                return float(slope), float(aspect)
            else:
                return np.nan, np.nan
                
        except Exception as e:
            return np.nan, np.nan
    
    def _weighted_linear_regression(self, elevations: np.ndarray, values: np.ndarray,
                                  target_elevation: float, weights: np.ndarray) -> float:
        """执行加权线性回归"""
        try:
            # 使用sklearn的线性回归，通过sample_weight参数实现加权
            reg = LinearRegression()
            
            # 重塑数据
            X = elevations.reshape(-1, 1)
            y = values
            
            # 拟合加权回归
            reg.fit(X, y, sample_weight=weights)
            
            # 预测目标高程的值
            predicted_value = reg.predict([[target_elevation]])[0]
            
            return predicted_value
            
        except Exception as e:
            # 如果回归失败，使用加权平均
            self.error_handler.log_warning(f"加权回归失败，使用加权平均: {str(e)}")
            return np.average(values, weights=weights)
    
    def _simple_distance_weighting(self, target_coord: np.ndarray,
                                  neighbor_coords: np.ndarray,
                                  neighbor_values: np.ndarray) -> float:
        """简单的距离权重插值（当高程数据不可用时）"""
        try:
            distances = cdist([target_coord], neighbor_coords)[0]
            weights = 1.0 / (distances ** self.distance_power + 1e-10)
            weights = weights / np.sum(weights)
            
            return np.sum(weights * neighbor_values)
            
        except Exception as e:
            return np.mean(neighbor_values)
    
    def _is_valid_pixel(self, pixel_index: int, mask: np.ndarray) -> bool:
        """检查像元是否在有效区域内"""
        try:
            if mask is None:
                return True
            
            if pixel_index < mask.size:
                mask_flat = mask.flatten()
                return mask_flat[pixel_index] != 0
            
            return True
            
        except Exception as e:
            return True
    
    def get_method_name(self) -> str:
        """获取方法名称"""
        return "PRISM"
    
    def get_parameters(self) -> dict:
        """获取当前参数"""
        return {
            'search_radius': self.search_radius,
            'min_stations': self.min_stations,
            'max_stations': self.max_stations,
            'distance_power': self.distance_power,
            'elevation_power': self.elevation_power
        }
    
    def set_parameters(self, **params) -> None:
        """设置参数"""
        if 'search_radius' in params:
            self.search_radius = params['search_radius']
        if 'distance_power' in params:
            self.distance_power = params['distance_power']
        if 'elevation_power' in params:
            self.elevation_power = params['elevation_power']
