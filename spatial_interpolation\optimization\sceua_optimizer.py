"""
降雨空间插值系统 - SCE-UA优化模块

实现SCE-UA算法的参数优化功能。

作者: Augment Agent
日期: 2025-06-26
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Callable, Any
import warnings

try:
    from sceua import sceua
    SCEUA_AVAILABLE = True
except ImportError:
    # 如果sceua不可用，使用scipy的差分进化算法作为替代
    try:
        from scipy.optimize import differential_evolution
        SCEUA_AVAILABLE = False
        SCIPY_AVAILABLE = True
        warnings.warn("sceua库未安装，将使用scipy.optimize.differential_evolution作为替代")
    except ImportError:
        SCEUA_AVAILABLE = False
        SCIPY_AVAILABLE = False
        warnings.warn("sceua和scipy都未安装，参数优化功能将不可用")

from ..utils.error_handler import OptimizationError, ErrorHandler
from ..evaluation.validator import CrossValidator, EvaluationMetrics


class SCEUAOptimizer:
    """SCE-UA参数优化器"""
    
    def __init__(self, config: dict, error_handler: Optional[ErrorHandler] = None):
        if not SCEUA_AVAILABLE and not SCIPY_AVAILABLE:
            raise OptimizationError("sceua和scipy都未安装，无法使用参数优化功能")
        
        self.config = config
        self.error_handler = error_handler or ErrorHandler()
        
        # 获取优化配置
        opt_config = config['optimization_settings']
        self.max_iterations = opt_config['sceua_params']['max_iterations']
        self.n_complexes = opt_config['sceua_params']['n_complexes']
        self.objective_function_type = opt_config['sceua_params']['objective_function']
        
        # 创建交叉验证器
        self.validator = CrossValidator(config, error_handler)
    
    def optimize_method_parameters(self, interpolator, method_name: str,
                                 station_coords: np.ndarray, station_values: np.ndarray,
                                 station_ids: List[str], target_stations: List[str]) -> Dict[str, Any]:
        """
        优化指定方法的参数
        
        参数:
            interpolator: 插值器对象
            method_name: 方法名称
            station_coords: 站点坐标
            station_values: 站点值
            station_ids: 站点ID列表
            target_stations: 目标站点列表
            
        返回:
            优化结果字典
        """
        try:
            self.error_handler.log_info(f"开始优化 {method_name} 方法的参数")
            
            # 获取参数边界
            bounds = self._get_parameter_bounds(method_name)
            
            if not bounds:
                raise OptimizationError(f"未找到方法 {method_name} 的参数边界配置")
            
            # 创建目标函数
            objective_func = self._create_objective_function(
                interpolator, station_coords, station_values, station_ids, target_stations
            )
            
            # 设置SCE-UA参数
            lower_bounds = np.array([bound[0] for bound in bounds])
            upper_bounds = np.array([bound[1] for bound in bounds])
            
            # 执行优化
            if SCEUA_AVAILABLE:
                self.error_handler.log_info(f"执行SCE-UA优化，参数范围: {bounds}")

                try:
                    result = sceua(
                        objective_func,
                        lower_bounds,
                        upper_bounds,
                        maxn=self.max_iterations,
                        kstop=10,  # 连续10次迭代无改善则停止
                        pcento=0.01,  # 收敛阈值
                        ngs=self.n_complexes
                    )

                    optimal_params = result['x']
                    optimal_value = result['fun']

                except Exception as e:
                    raise OptimizationError(f"SCE-UA优化执行失败: {str(e)}")

            else:  # 使用scipy的差分进化算法
                self.error_handler.log_info(f"执行差分进化优化，参数范围: {bounds}")

                try:
                    result = differential_evolution(
                        objective_func,
                        list(zip(lower_bounds, upper_bounds)),
                        maxiter=self.max_iterations // 10,  # 调整迭代次数
                        popsize=self.n_complexes * 3,  # 调整种群大小
                        seed=42
                    )

                    optimal_params = result.x
                    optimal_value = result.fun

                except Exception as e:
                    raise OptimizationError(f"差分进化优化执行失败: {str(e)}")

            self.error_handler.log_info(
                f"{method_name} 优化完成，最优目标函数值: {optimal_value:.6f}"
            )

            # 设置优化后的参数
            self._set_optimized_parameters(interpolator, method_name, optimal_params)

            # 验证优化结果
            validation_result = self._validate_optimized_parameters(
                interpolator, station_coords, station_values, station_ids, target_stations
            )

            return {
                'method': method_name,
                'optimal_parameters': optimal_params,
                'optimal_value': optimal_value,
                'parameter_bounds': bounds,
                'validation_result': validation_result,
                'optimization_info': result
            }
            
        except Exception as e:
            raise OptimizationError(f"优化方法 {method_name} 失败: {str(e)}")
    
    def _get_parameter_bounds(self, method_name: str) -> List[Tuple[float, float]]:
        """获取参数边界"""
        try:
            bounds_config = self.config['optimization_settings']['optimization_bounds']
            
            if method_name not in bounds_config:
                return []
            
            method_bounds = bounds_config[method_name]
            
            if method_name == 'Kriging':
                # [sill, range, nugget]
                return method_bounds['variogram_parameters']
            elif method_name == 'OI':
                # [observation_error_variance, background_error_corr_length]
                return method_bounds['params']
            elif method_name == 'PRISM':
                # [search_radius, distance_power, elevation_power]
                return method_bounds['params']
            else:
                return []
                
        except Exception as e:
            self.error_handler.log_warning(f"获取参数边界失败: {str(e)}")
            return []
    
    def _create_objective_function(self, interpolator, station_coords: np.ndarray,
                                 station_values: np.ndarray, station_ids: List[str],
                                 target_stations: List[str]) -> Callable:
        """创建目标函数"""
        def objective_function(params: np.ndarray) -> float:
            try:
                # 设置参数
                method_name = interpolator.get_method_name()
                self._set_parameters_from_array(interpolator, method_name, params)
                
                # 执行交叉验证
                if self.objective_function_type == 'weighted_rmse':
                    validation_result = self.validator.spatial_weighted_validation(
                        interpolator, station_coords, station_values, station_ids, target_stations
                    )
                    return validation_result['metrics'].get('Weighted_RMSE', np.inf)
                elif self.objective_function_type == 'nse_mean':
                    validation_result = self.validator.leave_one_out_validation(
                        interpolator, station_coords, station_values, station_ids, target_stations
                    )
                    nse = validation_result['metrics'].get('NSE', -np.inf)
                    return -nse  # 最小化负NSE等价于最大化NSE
                else:  # 默认使用RMSE
                    validation_result = self.validator.leave_one_out_validation(
                        interpolator, station_coords, station_values, station_ids, target_stations
                    )
                    return validation_result['metrics'].get('RMSE', np.inf)
                
            except Exception as e:
                self.error_handler.log_warning(f"目标函数计算失败: {str(e)}")
                return np.inf
        
        return objective_function
    
    def _set_parameters_from_array(self, interpolator, method_name: str, params: np.ndarray) -> None:
        """从参数数组设置插值器参数"""
        try:
            if method_name == 'Kriging':
                # [sill, range, nugget]
                interpolator.set_parameters(variogram_parameters=params.tolist())
            elif method_name == 'OI':
                # [observation_error_variance, background_error_corr_length]
                interpolator.set_parameters(
                    observation_error_variance=params[0],
                    background_error_corr_length=params[1]
                )
            elif method_name == 'PRISM':
                # [search_radius, distance_power, elevation_power]
                interpolator.set_parameters(
                    search_radius=params[0],
                    distance_power=params[1],
                    elevation_power=params[2]
                )
                
        except Exception as e:
            raise OptimizationError(f"设置参数失败: {str(e)}")
    
    def _set_optimized_parameters(self, interpolator, method_name: str, optimal_params: np.ndarray) -> None:
        """设置优化后的参数"""
        try:
            self._set_parameters_from_array(interpolator, method_name, optimal_params)
            
            # 记录优化后的参数
            param_dict = interpolator.get_parameters()
            self.error_handler.log_info(f"{method_name} 优化后参数: {param_dict}")
            
        except Exception as e:
            raise OptimizationError(f"设置优化参数失败: {str(e)}")
    
    def _validate_optimized_parameters(self, interpolator, station_coords: np.ndarray,
                                     station_values: np.ndarray, station_ids: List[str],
                                     target_stations: List[str]) -> Dict[str, Any]:
        """验证优化后的参数"""
        try:
            validation_result = self.validator.leave_one_out_validation(
                interpolator, station_coords, station_values, station_ids, target_stations
            )
            
            metrics = validation_result['metrics']
            self.error_handler.log_info(
                f"优化后验证结果 - RMSE: {metrics['RMSE']:.4f}, "
                f"MAE: {metrics['MAE']:.4f}, R2: {metrics['R2']:.4f}, NSE: {metrics['NSE']:.4f}"
            )
            
            return validation_result
            
        except Exception as e:
            self.error_handler.log_warning(f"验证优化参数失败: {str(e)}")
            return {}
    
    def optimize_multiple_methods(self, interpolators: Dict[str, Any],
                                station_coords: np.ndarray, station_values: np.ndarray,
                                station_ids: List[str], target_stations: List[str]) -> Dict[str, Any]:
        """优化多个方法的参数"""
        try:
            optimization_results = {}
            
            methods_to_optimize = self.config['optimization_settings']['methods_to_optimize']
            
            for method_name in methods_to_optimize:
                if method_name not in interpolators:
                    self.error_handler.log_warning(f"插值器 {method_name} 不存在，跳过优化")
                    continue
                
                try:
                    result = self.optimize_method_parameters(
                        interpolators[method_name], method_name,
                        station_coords, station_values, station_ids, target_stations
                    )
                    optimization_results[method_name] = result
                    
                except Exception as e:
                    self.error_handler.log_warning(f"优化方法 {method_name} 失败: {str(e)}")
                    continue
            
            return optimization_results
            
        except Exception as e:
            raise OptimizationError(f"批量优化失败: {str(e)}")
    
    def create_optimization_report(self, optimization_results: Dict[str, Any]) -> Dict[str, Any]:
        """创建优化报告"""
        try:
            report = {
                'optimization_summary': {},
                'parameter_comparison': {},
                'performance_comparison': {}
            }
            
            for method_name, result in optimization_results.items():
                # 优化摘要
                report['optimization_summary'][method_name] = {
                    'optimal_value': result['optimal_value'],
                    'optimal_parameters': result['optimal_parameters'],
                    'parameter_bounds': result['parameter_bounds']
                }
                
                # 性能比较
                if 'validation_result' in result and 'metrics' in result['validation_result']:
                    metrics = result['validation_result']['metrics']
                    report['performance_comparison'][method_name] = metrics
            
            return report
            
        except Exception as e:
            raise OptimizationError(f"创建优化报告失败: {str(e)}")
    
    def is_optimization_enabled(self) -> bool:
        """检查是否启用了参数优化"""
        return self.config['optimization_settings']['enable_optimization']
    
    def get_methods_to_optimize(self) -> List[str]:
        """获取需要优化的方法列表"""
        return self.config['optimization_settings']['methods_to_optimize']
