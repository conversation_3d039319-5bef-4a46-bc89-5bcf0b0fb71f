"""
降雨空间插值系统 - 测试脚本

用于验证系统安装和基本功能。

作者: Augment Agent
日期: 2025-06-26
"""

import sys
import os
import numpy as np
import pandas as pd
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """测试所有必要的包导入"""
    print("测试包导入...")
    
    try:
        import numpy
        print("✓ numpy")
    except ImportError as e:
        print(f"✗ numpy: {e}")
        return False
    
    try:
        import pandas
        print("✓ pandas")
    except ImportError as e:
        print(f"✗ pandas: {e}")
        return False
    
    try:
        import rasterio
        print("✓ rasterio")
    except ImportError as e:
        print(f"✗ rasterio: {e}")
        return False
    
    try:
        import pykrige
        print("✓ pykrige")
    except ImportError as e:
        print(f"✗ pykrige: {e}")
        return False
    
    try:
        import sceua
        print("✓ sceua")
    except ImportError as e:
        print(f"✗ sceua: {e}")
        return False
    
    try:
        import yaml
        print("✓ yaml")
    except ImportError as e:
        print(f"✗ yaml: {e}")
        return False
    
    return True

def test_config_loading():
    """测试配置文件加载"""
    print("\n测试配置文件加载...")
    
    try:
        import yaml
        config_path = Path("config/config.yml")
        
        if not config_path.exists():
            print(f"✗ 配置文件不存在: {config_path}")
            return False
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 检查必要的配置项
        required_keys = [
            'project_paths', 'run_settings', 'method_params', 
            'optimization_settings', 'error_handling'
        ]
        
        for key in required_keys:
            if key not in config:
                print(f"✗ 配置文件缺少必要项: {key}")
                return False
        
        print("✓ 配置文件加载成功")
        return True
        
    except Exception as e:
        print(f"✗ 配置文件加载失败: {e}")
        return False

def test_interpolators():
    """测试插值器初始化"""
    print("\n测试插值器初始化...")
    
    try:
        # 创建模拟配置
        config = {
            'method_params': {
                'IDW': {'power': 2.0},
                'Kriging': {
                    'variogram_model': 'spherical',
                    'variogram_parameters': [10.0, 100000.0, 1.0],
                    'anisotropy_scaling': 1.0,
                    'anisotropy_angle': 0.0,
                    'zero_handling': 'standard',
                    'zero_threshold': 0.7
                },
                'OI': {
                    'observation_error_variance': 0.5,
                    'background_error_corr_length': 50000.0
                },
                'PRISM': {
                    'search_radius': 150000.0,
                    'min_stations': 5,
                    'max_stations': 20,
                    'distance_power': 2.0,
                    'elevation_power': 1.0
                }
            },
            'run_settings': {'cpu_cores': 2},
            'error_handling': {'use_pseudo_inverse': True},
            'grid_settings': {'nodata_value': -9999}
        }
        
        from utils.error_handler import ErrorHandler
        error_handler = ErrorHandler(verbose=False)
        
        # 测试IDW
        from core.idw import IDWInterpolator
        idw = IDWInterpolator(config, error_handler)
        print("✓ IDW插值器")
        
        # 测试Kriging
        from core.kriging import KrigingInterpolator
        kriging = KrigingInterpolator(config, error_handler)
        print("✓ Kriging插值器")
        
        # 测试OI
        from core.oi import OIInterpolator
        oi = OIInterpolator(config, error_handler)
        print("✓ OI插值器")
        
        # 测试PRISM
        from core.prism import PRISMInterpolator
        prism = PRISMInterpolator(config, error_handler)
        print("✓ PRISM插值器")
        
        return True
        
    except Exception as e:
        print(f"✗ 插值器初始化失败: {e}")
        return False

def test_simple_interpolation():
    """测试简单插值计算"""
    print("\n测试简单插值计算...")
    
    try:
        # 创建测试数据
        np.random.seed(42)
        
        # 站点坐标和值
        station_coords = np.array([
            [0, 0], [1, 0], [0, 1], [1, 1], [0.5, 0.5]
        ])
        station_values = np.array([1.0, 2.0, 3.0, 4.0, 2.5])
        
        # 目标坐标
        target_coords = np.array([
            [0.25, 0.25], [0.75, 0.75]
        ])
        
        # 配置
        config = {
            'method_params': {
                'IDW': {'power': 2.0}
            },
            'run_settings': {'cpu_cores': 1},
            'grid_settings': {'nodata_value': -9999}
        }
        
        from utils.error_handler import ErrorHandler
        from core.idw import IDWInterpolator
        
        error_handler = ErrorHandler(verbose=False)
        idw = IDWInterpolator(config, error_handler)
        
        # 执行插值
        result = idw.interpolate(station_coords, station_values, target_coords)
        
        if len(result) == len(target_coords):
            print(f"✓ IDW插值计算成功，结果: {result}")
            return True
        else:
            print(f"✗ IDW插值结果长度不匹配")
            return False
        
    except Exception as e:
        print(f"✗ 简单插值计算失败: {e}")
        return False

def test_data_structure():
    """测试数据目录结构"""
    print("\n测试数据目录结构...")
    
    try:
        # 检查基础目录
        base_dir = Path("../")  # 相对于spatial_interpolation目录
        
        required_dirs = [
            "input_another",
            "terrain/90",
        ]
        
        required_files = [
            "stations.csv",
            "Delaunay_Molan.csv",
            "terrain/90/mask.asc"
        ]
        
        for dir_path in required_dirs:
            full_path = base_dir / dir_path
            if full_path.exists():
                print(f"✓ 目录存在: {dir_path}")
            else:
                print(f"✗ 目录不存在: {dir_path}")
                return False
        
        for file_path in required_files:
            full_path = base_dir / file_path
            if full_path.exists():
                print(f"✓ 文件存在: {file_path}")
            else:
                print(f"✗ 文件不存在: {file_path}")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 数据结构检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("="*60)
    print("降雨空间插值系统 - 系统测试")
    print("="*60)
    
    tests = [
        ("包导入测试", test_imports),
        ("配置文件测试", test_config_loading),
        ("插值器测试", test_interpolators),
        ("简单插值测试", test_simple_interpolation),
        ("数据结构测试", test_data_structure)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 40)
        
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "="*60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统准备就绪。")
        return True
    else:
        print("❌ 部分测试失败，请检查安装和配置。")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
